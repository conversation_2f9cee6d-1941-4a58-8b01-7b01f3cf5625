package middleware

import (
	"context"
	"loop/internal/client"
	"loop/internal/constants"
	"loop/pkg/i18n"
	"loop/pkg/jwtx"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func JWT(jwtSecret string, client *client.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Request.Header.Get("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenIsEmpty),
			})
			logrus.Error("uid", jwtx.GetUid(c), "token is empty")
			c.Abort()
			return
		}

		// 先验证 JWT token
		claims, err := jwtx.ParseToken(token, jwtSecret)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenParse),
			})
			logrus.Error("uid", jwtx.GetUid(c), "token parse error", err)
			c.Abort()
			return
		} else if time.Now().Unix() > claims.ExpiresAt {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenExpire),
			})
			logrus.Error("uid", jwtx.GetUid(c), "token expire")
			c.Abort()
			return
		}

		// 再验证 Redis 中存储的 token
		storedToken, err := client.RedisClient.Get(context.Background(), constants.UserTokenPrefix+claims.Uid).Result()
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenNotFound),
			})
			logrus.Error("uid", jwtx.GetUid(c), "token not found")
			c.Abort()
			return
		}

		// 验证当前 token 是否与 Redis 中存储的一致
		if storedToken != token {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrTokenMismatch),
			})
			logrus.Error("uid", jwtx.GetUid(c), "token mismatch")
			c.Abort()
			return
		}

		if claims.Status == 1 {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrUnauthorized),
			})
			logrus.Error("uid", jwtx.GetUid(c), "status =1 andunauthorized")
			c.Abort()
			return
		}
		if claims.Uid == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": http.StatusUnauthorized,
				"msg":  i18n.T(c, i18n.ErrUnauthorized),
			})
			logrus.Error("uid", jwtx.GetUid(c), "claims.Uid empty and unauthorized")
			c.Abort()
			return
		}
		c.Set("uid", claims.Uid)
		c.Next()
	}
}
