package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"loop/internal/config"
	"loop/internal/constants"
	"loop/internal/model"
)

// loadConfig 加载配置文件，自动检测路径
func loadConfig() (*config.Config, error) {
	// 尝试不同的配置文件路径
	configPaths := []string{
		"configs/config.yaml",       // 从项目根目录运行
		"../configs/config.yaml",    // 从scripts目录运行
		"../../configs/config.yaml", // 从scripts/init目录运行
	}

	var configFile string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configFile = path
			break
		}
	}

	if configFile == "" {
		return nil, fmt.Errorf("config file not found in any of the expected paths")
	}

	cfg := &config.Config{}
	yamlFile, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %v", configFile, err)
	}

	if err = yaml.Unmarshal(yamlFile, cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %v", err)
	}

	fmt.Fprintf(os.Stderr, "配置文件加载成功: %s\n", configFile)
	return cfg, nil
}

func main() {
	fmt.Println("=== 开始初始化管理员数据 ===")
	os.Stdout.Sync()

	// 加载配置
	fmt.Println("正在加载配置文件...")
	os.Stdout.Sync()
	cfg, err := loadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	fmt.Println("配置加载完成")
	fmt.Printf("数据库URL: %s\n", cfg.DbConfig.Url)
	os.Stdout.Sync()

	// 连接数据库
	fmt.Println("正在连接数据库...")

	db, err := connectDB(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	fmt.Println("数据库连接成功")

	// 初始化数据
	fmt.Println("开始初始化数据...")
	if err := initAdminData(db); err != nil {
		log.Fatalf("Failed to initialize admin data: %v", err)
	}

	fmt.Println("Admin data initialization completed successfully!")

	// 验证数据
	fmt.Println("=== 开始数据验证 ===")
	if err := verifyData(db); err != nil {
		log.Fatalf("Data verification failed: %v", err)
	}
	fmt.Println("=== 数据验证完成 ===")
}

func connectDB(cfg *config.Config) (*gorm.DB, error) {
	fmt.Printf("尝试连接数据库: %s\n", cfg.DbConfig.Url)

	// 直接使用配置中的DSN URL
	db, err := gorm.Open(mysql.Open(cfg.DbConfig.Url), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		fmt.Printf("数据库连接失败: %v\n", err)
		return nil, err
	}

	fmt.Println("数据库连接成功，开始自动迁移表结构...")

	// 自动迁移表结构
	if err := db.AutoMigrate(model.Models...); err != nil {
		fmt.Printf("表结构迁移失败: %v\n", err)
		return nil, fmt.Errorf("failed to auto migrate tables: %v", err)
	}

	fmt.Println("表结构迁移完成")
	return db, nil
}

func initAdminData(db *gorm.DB) error {
	// 初始化系统角色
	fmt.Println("正在初始化系统角色...")
	if err := initSysRoles(db); err != nil {
		return fmt.Errorf("failed to init sys roles: %v", err)
	}
	fmt.Println("系统角色初始化完成")

	// 初始化系统用户
	fmt.Println("正在初始化系统用户...")
	if err := initSysUsers(db); err != nil {
		return fmt.Errorf("failed to init sys users: %v", err)
	}
	fmt.Println("系统用户初始化完成")

	// 初始化分类类型
	fmt.Println("正在初始化分类类型...")
	if err := initCategoryTypes(db); err != nil {
		return fmt.Errorf("failed to init category types: %v", err)
	}
	fmt.Println("分类类型初始化完成")

	// 初始化分类
	fmt.Println("正在初始化分类...")
	if err := initCategories(db); err != nil {
		return fmt.Errorf("failed to init categories: %v", err)
	}
	fmt.Println("分类初始化完成")

	// 初始化VIP等级
	if err := initVIPs(db); err != nil {
		return fmt.Errorf("failed to init VIPs: %v", err)
	}

	// 初始化商品
	if err := initTradeProducts(db); err != nil {
		return fmt.Errorf("failed to init trade products: %v", err)
	}

	// 初始化权益组
	if err := initBenefitGroups(db); err != nil {
		return fmt.Errorf("failed to init benefit groups: %v", err)
	}

	// 初始化权益
	if err := initBenefits(db); err != nil {
		return fmt.Errorf("failed to init benefits: %v", err)
	}

	// 初始化VIP权益关联
	if err := initVipBenefits(db); err != nil {
		return fmt.Errorf("failed to init vip benefits: %v", err)
	}

	return nil
}

func initSysRoles(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM sys_roles").Error; err != nil {
		return err
	}

	roles := []model.SysRole{
		{
			Name:      "admin",
			RoleLevel: 2,
		},
		{
			Name:      "normal",
			RoleLevel: 0,
		},
	}

	for _, role := range roles {
		if err := db.Create(&role).Error; err != nil {
			return err
		}
	}

	return nil
}

func initSysUsers(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM sys_users").Error; err != nil {
		return err
	}
	bytes, _ := bcrypt.GenerateFromPassword([]byte("19930118"), constants.PassWordCost)
	users := []model.SysUser{
		{
			UserName:       "admin",
			PasswordDigest: string(bytes), // secret
			RoleLevel:      2,
			Status:         0,
		},
	}

	for _, user := range users {
		if err := db.Create(&user).Error; err != nil {
			return err
		}
	}

	return nil
}

func initCategoryTypes(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM category_types").Error; err != nil {
		return err
	}

	categoryTypes := []model.CategoryType{
		{
			Name:        "等级",
			Description: "学习者的英语水平等级分类",
			Priority:    1,
		},
		{
			Name:        "场景",
			Description: "不同的学习和应用场景分类",
			Priority:    2,
		},
		{
			Name:        "主题",
			Description: "学习资源的主题内容分类",
			Priority:    3,
		},
	}

	for _, categoryType := range categoryTypes {
		if err := db.Create(&categoryType).Error; err != nil {
			return err
		}
	}

	return nil
}

func initCategories(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM categories").Error; err != nil {
		return err
	}

	// 获取已创建的CategoryType记录
	var categoryTypes []model.CategoryType
	if err := db.Order("priority").Find(&categoryTypes).Error; err != nil {
		return fmt.Errorf("failed to get category types: %v", err)
	}

	if len(categoryTypes) < 3 {
		return fmt.Errorf("expected at least 3 category types, got %d", len(categoryTypes))
	}

	// 按优先级排序，应该是：等级(1)、场景(2)、主题(3)
	levelTypeId := categoryTypes[0].Id // 等级
	sceneTypeId := categoryTypes[1].Id // 场景
	topicTypeId := categoryTypes[2].Id // 主题

	categories := []model.Category{
		// 等级分类
		{
			Name:           "零基础",
			Description:    "适合完全没有英语基础的学习者",
			Priority:       1,
			CategoryTypeId: levelTypeId,
		},
		{
			Name:           "中级",
			Description:    "适合有一定英语基础的学习者",
			Priority:       2,
			CategoryTypeId: levelTypeId,
		},
		{
			Name:           "高级",
			Description:    "适合英语基础较好的学习者",
			Priority:       3,
			CategoryTypeId: levelTypeId,
		},

		// 场景分类
		{
			Name:           "演讲",
			Description:    "公开演讲和演示技巧",
			Priority:       1,
			CategoryTypeId: sceneTypeId,
		},
		{
			Name:           "考试",
			Description:    "各类英语考试准备",
			Priority:       2,
			CategoryTypeId: sceneTypeId,
		},
		{
			Name:           "面试",
			Description:    "求职面试英语技巧",
			Priority:       3,
			CategoryTypeId: sceneTypeId,
		},
		{
			Name:           "出国",
			Description:    "出国留学和生活英语",
			Priority:       4,
			CategoryTypeId: sceneTypeId,
		},
		{
			Name:           "写作",
			Description:    "英语写作技巧和练习",
			Priority:       5,
			CategoryTypeId: sceneTypeId,
		},

		// 主题分类
		{
			Name:           "TED",
			Description:    "TED演讲学习资源",
			Priority:       1,
			CategoryTypeId: topicTypeId,
		},
		{
			Name:           "YouTube",
			Description:    "YouTube视频学习资源",
			Priority:       2,
			CategoryTypeId: topicTypeId,
		},
		{
			Name:           "博客",
			Description:    "英语博客和文章",
			Priority:       3,
			CategoryTypeId: topicTypeId,
		},
		{
			Name:           "影视",
			Description:    "电影和电视剧学习资源",
			Priority:       4,
			CategoryTypeId: topicTypeId,
		},
		{
			Name:           "书籍",
			Description:    "英语书籍和文学作品",
			Priority:       5,
			CategoryTypeId: topicTypeId,
		},
	}

	for _, category := range categories {
		if err := db.Create(&category).Error; err != nil {
			return err
		}
	}

	return nil
}

func initVIPs(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM vips").Error; err != nil {
		return err
	}

	vips := []model.VIP{
		{
			Name:  "普通会员",
			Level: 1,
		},
		{
			Name:  "PRO会员",
			Level: 100,
		},
		{
			Name:  "ULTRA会员",
			Level: 1000,
		},
	}

	for _, vip := range vips {
		if err := db.Create(&vip).Error; err != nil {
			return err
		}
	}

	return nil
}

func initTradeProducts(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM trade_products").Error; err != nil {
		return err
	}

	products := []model.TradeProduct{
		{
			Name:           "PRO会员1个月",
			Type:           1,
			IsSubscription: 1,
			Price:          29.00,
			OriginPrice:    29.00,
			IosProductId:   "com.lsenglish.pro.month",
			Currency:       "USD",
			Terminal:       1, // Android
			Days:           30,
			VipID:          2,
		},
		{
			Name:           "PRO会员3个月",
			Type:           1,
			IsSubscription: 1,
			Price:          69.00,
			OriginPrice:    69.00,
			IosProductId:   "com.lsenglish.pro.quarter",
			Currency:       "USD",
			Terminal:       1, // Android
			Days:           90,
			VipID:          2,
		},
		{
			Name:           "ULTRA会员1个月",
			Type:           1,
			IsSubscription: 1,
			Price:          49.00,
			OriginPrice:    49.00,
			IosProductId:   "com.lsenglish.ultra.month",
			Currency:       "USD",
			Terminal:       1, // Android
			Days:           30,
			VipID:          3,
		},
		{
			Name:           "PRO会员1个月",
			Type:           1,
			IsSubscription: 1,
			Price:          29.00,
			OriginPrice:    29.00,
			IosProductId:   "com.lsenglish.pro.month",
			Currency:       "USD",
			Terminal:       2, // iOS
			Days:           30,
			VipID:          2,
		},
		{
			Name:           "PRO会员3个月",
			Type:           1,
			IsSubscription: 1,
			Price:          69.00,
			OriginPrice:    69.00,
			IosProductId:   "com.lsenglish.pro.quarter",
			Currency:       "USD",
			Terminal:       2, // iOS
			Days:           90,
			VipID:          2,
		},
		{
			Name:           "ULTRA会员1个月",
			Type:           1,
			IsSubscription: 1,
			Price:          49.00,
			OriginPrice:    49.00,
			IosProductId:   "com.lsenglish.ultra.month",
			Currency:       "USD",
			Terminal:       2, // iOS
			Days:           30,
			VipID:          3,
		},
	}

	for _, product := range products {
		if err := db.Create(&product).Error; err != nil {
			return err
		}
	}

	return nil
}

func initBenefitGroups(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM benefit_groups").Error; err != nil {
		return err
	}

	groups := []model.BenefitGroup{
		{
			Name:        "单次视频大小限制组",
			Code:        "VIDEO_SIZE_LIMIT",
			Status:      1,
			Description: "单次上传视频文件大小限制",
		},
		{
			Name:        "总视频大小限制组",
			Code:        "TOTAL_VIDEO_SIZE_LIMIT",
			Status:      1,
			Description: "用户总视频上传大小限制",
		},
		{
			Name:        "AI调用次数组",
			Code:        "AI_CALLS_LIMIT",
			Status:      1,
			Description: "AI服务调用次数限制",
		},
		{
			Name:        "字幕对话次数组",
			Code:        "SUBTITLE_DIALOGUE_LIMIT",
			Status:      1,
			Description: "字幕对话功能使用次数限制",
		},
		{
			Name:        "语音转写时长组",
			Code:        "SPEECH_TO_TEXT_LIMIT",
			Status:      1,
			Description: "语音转写文字功能时长限制",
		},
	}

	for _, group := range groups {
		if err := db.Create(&group).Error; err != nil {
			return err
		}
	}

	return nil
}

func initBenefits(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM benefits").Error; err != nil {
		return err
	}

	// 获取已创建的权益组记录
	var benefitGroups []model.BenefitGroup
	if err := db.Order("id").Find(&benefitGroups).Error; err != nil {
		return fmt.Errorf("failed to get benefit groups: %v", err)
	}

	if len(benefitGroups) < 5 {
		return fmt.Errorf("expected at least 5 benefit groups, got %d", len(benefitGroups))
	}

	// 通过Code查找对应的权益组
	groupMap := make(map[string]model.BenefitGroup)
	for _, group := range benefitGroups {
		groupMap[group.Code] = group
	}

	videoSizeGroup := groupMap["VIDEO_SIZE_LIMIT"]
	totalVideoSizeGroup := groupMap["TOTAL_VIDEO_SIZE_LIMIT"]
	aiCallsGroup := groupMap["AI_CALLS_LIMIT"]
	subtitleDialogueGroup := groupMap["SUBTITLE_DIALOGUE_LIMIT"]
	speechToTextGroup := groupMap["SPEECH_TO_TEXT_LIMIT"]

	benefits := []model.Benefit{
		// 普通会员权益
		{
			Name:             "普通会员单次视频限制",
			Code:             "UPLOAD_LIMIT_BASIC",
			Level:            10,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     50, // 50MB
			Sort:             1,
			Status:           1,
			BenefitGroupID:   videoSizeGroup.Id,
			BenefitGroupName: videoSizeGroup.Name,
			BenefitGroupCode: videoSizeGroup.Code,
			Description:      "普通会员单次视频最大50MB",
		},
		{
			Name:             "普通会员总视频限制",
			Code:             "TOTAL_UPLOAD_LIMIT_BASIC",
			Level:            10,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     1024, // 1GB
			Sort:             1,
			Status:           1,
			BenefitGroupID:   totalVideoSizeGroup.Id,
			BenefitGroupName: totalVideoSizeGroup.Name,
			BenefitGroupCode: totalVideoSizeGroup.Code,
			Description:      "普通会员总视频最大1GB",
		},
		{
			Name:             "普通会员AI调用次数",
			Code:             "AI_CALLS_BASIC",
			Level:            10,
			CycleType:        1, // 日周期
			CycleCount:       1,
			BenefitCount:     10, // 每日10次
			Sort:             1,
			Status:           1,
			BenefitGroupID:   aiCallsGroup.Id,
			BenefitGroupName: aiCallsGroup.Name,
			BenefitGroupCode: aiCallsGroup.Code,
			Description:      "普通会员每日10次AI调用",
		},
		{
			Name:             "普通会员字幕对话次数",
			Code:             "SUBTITLE_DIALOGUE_BASIC",
			Level:            10,
			CycleType:        1, // 日周期
			CycleCount:       1,
			BenefitCount:     5, // 每日5次
			Sort:             1,
			Status:           1,
			BenefitGroupID:   subtitleDialogueGroup.Id,
			BenefitGroupName: subtitleDialogueGroup.Name,
			BenefitGroupCode: subtitleDialogueGroup.Code,
			Description:      "普通会员每日5次字幕对话",
		},
		{
			Name:             "普通会员语音转写时长",
			Code:             "SPEECH_TO_TEXT_BASIC",
			Level:            10,
			CycleType:        3, // 月周期
			CycleCount:       1,
			BenefitCount:     60, // 每月60分钟（1小时）
			Sort:             1,
			Status:           1,
			BenefitGroupID:   speechToTextGroup.Id,
			BenefitGroupName: speechToTextGroup.Name,
			BenefitGroupCode: speechToTextGroup.Code,
			Description:      "普通会员每月1小时语音转写时长",
		},

		// PRO会员权益
		{
			Name:             "Pro会员单次视频限制",
			Code:             "UPLOAD_LIMIT_PRO",
			Level:            20,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     200, // 200MB
			Sort:             2,
			Status:           1,
			BenefitGroupID:   videoSizeGroup.Id,
			BenefitGroupName: videoSizeGroup.Name,
			BenefitGroupCode: videoSizeGroup.Code,
			Description:      "Pro会员单次视频最大200MB",
		},
		{
			Name:             "Pro会员总视频限制",
			Code:             "TOTAL_UPLOAD_LIMIT_PRO",
			Level:            20,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     5120, // 5GB
			Sort:             2,
			Status:           1,
			BenefitGroupID:   totalVideoSizeGroup.Id,
			BenefitGroupName: totalVideoSizeGroup.Name,
			BenefitGroupCode: totalVideoSizeGroup.Code,
			Description:      "Pro会员总视频最大5GB",
		},
		{
			Name:             "Pro会员AI调用次数",
			Code:             "AI_CALLS_PRO",
			Level:            20,
			CycleType:        1, // 日周期
			CycleCount:       1,
			BenefitCount:     100, // 每日100次
			Sort:             2,
			Status:           1,
			BenefitGroupID:   aiCallsGroup.Id,
			BenefitGroupName: aiCallsGroup.Name,
			BenefitGroupCode: aiCallsGroup.Code,
			Description:      "Pro会员每日100次AI调用",
		},
		{
			Name:             "Pro会员字幕对话次数",
			Code:             "SUBTITLE_DIALOGUE_PRO",
			Level:            20,
			CycleType:        1, // 日周期
			CycleCount:       1,
			BenefitCount:     50, // 每日50次
			Sort:             2,
			Status:           1,
			BenefitGroupID:   subtitleDialogueGroup.Id,
			BenefitGroupName: subtitleDialogueGroup.Name,
			BenefitGroupCode: subtitleDialogueGroup.Code,
			Description:      "Pro会员每日50次字幕对话",
		},
		{
			Name:             "Pro会员语音转写时长",
			Code:             "SPEECH_TO_TEXT_PRO",
			Level:            20,
			CycleType:        3, // 月周期
			CycleCount:       1,
			BenefitCount:     600, // 每月600分钟（10小时）
			Sort:             2,
			Status:           1,
			BenefitGroupID:   speechToTextGroup.Id,
			BenefitGroupName: speechToTextGroup.Name,
			BenefitGroupCode: speechToTextGroup.Code,
			Description:      "Pro会员每月10小时语音转写时长",
		},

		// ULTRA会员权益
		{
			Name:             "Ultra会员单次视频限制",
			Code:             "UPLOAD_LIMIT_ULTRA",
			Level:            30,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     10240, // 10GB
			Sort:             3,
			Status:           1,
			BenefitGroupID:   videoSizeGroup.Id,
			BenefitGroupName: videoSizeGroup.Name,
			BenefitGroupCode: videoSizeGroup.Code,
			Description:      "Ultra会员单次视频最大10GB",
		},
		{
			Name:             "Ultra会员总视频限制",
			Code:             "TOTAL_UPLOAD_LIMIT_ULTRA",
			Level:            30,
			CycleType:        6, // 无周期
			CycleCount:       1,
			BenefitCount:     51200, // 50GB
			Sort:             3,
			Status:           1,
			BenefitGroupID:   totalVideoSizeGroup.Id,
			BenefitGroupName: totalVideoSizeGroup.Name,
			BenefitGroupCode: totalVideoSizeGroup.Code,
			Description:      "Ultra会员总视频最大50GB",
		},
		{
			Name:             "Ultra会员AI调用次数",
			Code:             "AI_CALLS_ULTRA",
			Level:            30,
			CycleType:        3, // 月周期
			CycleCount:       1,
			BenefitCount:     1000, // 每月1000次
			Sort:             3,
			Status:           1,
			BenefitGroupID:   aiCallsGroup.Id,
			BenefitGroupName: aiCallsGroup.Name,
			BenefitGroupCode: aiCallsGroup.Code,
			Description:      "Ultra会员每月1000次AI调用",
		},
		{
			Name:             "Ultra会员字幕对话次数",
			Code:             "SUBTITLE_DIALOGUE_ULTRA",
			Level:            30,
			CycleType:        3, // 月周期
			CycleCount:       1,
			BenefitCount:     1000, // 每月1000次
			Sort:             3,
			Status:           1,
			BenefitGroupID:   subtitleDialogueGroup.Id,
			BenefitGroupName: subtitleDialogueGroup.Name,
			BenefitGroupCode: subtitleDialogueGroup.Code,
			Description:      "Ultra会员每月1000次字幕对话",
		},
		{
			Name:             "Ultra会员语音转写时长",
			Code:             "SPEECH_TO_TEXT_ULTRA",
			Level:            30,
			CycleType:        3, // 月周期
			CycleCount:       1,
			BenefitCount:     1800, // 每月1800分钟（30小时）
			Sort:             3,
			Status:           1,
			BenefitGroupID:   speechToTextGroup.Id,
			BenefitGroupName: speechToTextGroup.Name,
			BenefitGroupCode: speechToTextGroup.Code,
			Description:      "Ultra会员每月30小时语音转写时长",
		},
	}

	for _, benefit := range benefits {
		if err := db.Create(&benefit).Error; err != nil {
			return err
		}
	}

	return nil
}

func initVipBenefits(db *gorm.DB) error {
	// 删除现有数据
	if err := db.Exec("DELETE FROM vip_benefits").Error; err != nil {
		return err
	}

	// 获取已创建的VIP、权益组和权益的ID
	var vips []model.VIP
	if err := db.Order("level").Find(&vips).Error; err != nil {
		return err
	}

	var benefitGroups []model.BenefitGroup
	if err := db.Order("id").Find(&benefitGroups).Error; err != nil {
		return err
	}

	var benefits []model.Benefit
	if err := db.Order("level, sort").Find(&benefits).Error; err != nil {
		return err
	}

	// 通过Code查找对应的权益组
	groupMap := make(map[string]model.BenefitGroup)
	for _, group := range benefitGroups {
		groupMap[group.Code] = group
	}

	// 创建VIP权益关联关系
	vipBenefitMappings := []struct {
		VipLevel    int
		BenefitCode string
		GroupCode   string
	}{
		// 普通会员权益关联
		{1, "UPLOAD_LIMIT_BASIC", "VIDEO_SIZE_LIMIT"},
		{1, "TOTAL_UPLOAD_LIMIT_BASIC", "TOTAL_VIDEO_SIZE_LIMIT"},
		{1, "AI_CALLS_BASIC", "AI_CALLS_LIMIT"},
		{1, "SUBTITLE_DIALOGUE_BASIC", "SUBTITLE_DIALOGUE_LIMIT"},
		{1, "SPEECH_TO_TEXT_BASIC", "SPEECH_TO_TEXT_LIMIT"},

		// PRO会员权益关联
		{100, "UPLOAD_LIMIT_PRO", "VIDEO_SIZE_LIMIT"},
		{100, "TOTAL_UPLOAD_LIMIT_PRO", "TOTAL_VIDEO_SIZE_LIMIT"},
		{100, "AI_CALLS_PRO", "AI_CALLS_LIMIT"},
		{100, "SUBTITLE_DIALOGUE_PRO", "SUBTITLE_DIALOGUE_LIMIT"},
		{100, "SPEECH_TO_TEXT_PRO", "SPEECH_TO_TEXT_LIMIT"},

		// ULTRA会员权益关联
		{1000, "UPLOAD_LIMIT_ULTRA", "VIDEO_SIZE_LIMIT"},
		{1000, "TOTAL_UPLOAD_LIMIT_ULTRA", "TOTAL_VIDEO_SIZE_LIMIT"},
		{1000, "AI_CALLS_ULTRA", "AI_CALLS_LIMIT"},
		{1000, "SUBTITLE_DIALOGUE_ULTRA", "SUBTITLE_DIALOGUE_LIMIT"},
		{1000, "SPEECH_TO_TEXT_ULTRA", "SPEECH_TO_TEXT_LIMIT"},
	}

	for _, mapping := range vipBenefitMappings {
		// 找到对应的VIP
		var vip model.VIP
		for _, v := range vips {
			if v.Level == mapping.VipLevel {
				vip = v
				break
			}
		}

		// 找到对应的权益
		var benefit model.Benefit
		for _, b := range benefits {
			if string(b.Code) == mapping.BenefitCode {
				benefit = b
				break
			}
		}

		// 通过GroupCode获取权益组ID
		benefitGroup := groupMap[mapping.GroupCode]

		// 创建VIP权益关联
		vipBenefit := model.VipBenefit{
			VipID:           vip.Id,
			VipLevel:        uint(vip.Level),
			BenefitGroupID:  benefitGroup.Id,
			BenefitID:       benefit.Id,
			BenefitCode:     benefit.Code,
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().Unix(),
		}

		if err := db.Create(&vipBenefit).Error; err != nil {
			return err
		}
	}

	return nil
}

func verifyData(db *gorm.DB) error {
	// 验证系统角色
	var roleCount int64
	if err := db.Model(&model.SysRole{}).Count(&roleCount).Error; err != nil {
		return fmt.Errorf("failed to count sys roles: %v", err)
	}
	fmt.Printf("✓ 系统角色数量: %d\n", roleCount)

	// 验证系统用户
	var userCount int64
	if err := db.Model(&model.SysUser{}).Count(&userCount).Error; err != nil {
		return fmt.Errorf("failed to count sys users: %v", err)
	}
	fmt.Printf("✓ 系统用户数量: %d\n", userCount)

	// 验证分类类型
	var categoryTypeCount int64
	if err := db.Model(&model.CategoryType{}).Count(&categoryTypeCount).Error; err != nil {
		return fmt.Errorf("failed to count category types: %v", err)
	}
	fmt.Printf("✓ 分类类型数量: %d\n", categoryTypeCount)

	// 验证分类
	var categoryCount int64
	if err := db.Model(&model.Category{}).Count(&categoryCount).Error; err != nil {
		return fmt.Errorf("failed to count categories: %v", err)
	}
	fmt.Printf("✓ 分类数量: %d\n", categoryCount)

	// 验证VIP等级
	var vipCount int64
	if err := db.Model(&model.VIP{}).Count(&vipCount).Error; err != nil {
		return fmt.Errorf("failed to count vips: %v", err)
	}
	fmt.Printf("✓ VIP等级数量: %d\n", vipCount)

	// 验证商品
	var productCount int64
	if err := db.Model(&model.TradeProduct{}).Count(&productCount).Error; err != nil {
		return fmt.Errorf("failed to count trade products: %v", err)
	}
	fmt.Printf("✓ 商品数量: %d\n", productCount)

	// 验证权益组
	var benefitGroupCount int64
	if err := db.Model(&model.BenefitGroup{}).Count(&benefitGroupCount).Error; err != nil {
		return fmt.Errorf("failed to count benefit groups: %v", err)
	}
	fmt.Printf("✓ 权益组数量: %d\n", benefitGroupCount)

	// 验证权益
	var benefitCount int64
	if err := db.Model(&model.Benefit{}).Count(&benefitCount).Error; err != nil {
		return fmt.Errorf("failed to count benefits: %v", err)
	}
	fmt.Printf("✓ 权益数量: %d\n", benefitCount)

	// 验证VIP权益关联
	var vipBenefitCount int64
	if err := db.Model(&model.VipBenefit{}).Count(&vipBenefitCount).Error; err != nil {
		return fmt.Errorf("failed to count vip benefits: %v", err)
	}
	fmt.Printf("✓ VIP权益关联数量: %d\n", vipBenefitCount)

	// 验证管理员用户是否可以登录
	var admin model.SysUser
	if err := db.Where("username = ?", "admin").First(&admin).Error; err != nil {
		return fmt.Errorf("admin user not found: %v", err)
	}
	fmt.Printf("✓ 管理员用户验证成功: %s (角色等级: %d)\n", admin.UserName, admin.RoleLevel)

	fmt.Println("所有数据验证通过！")
	return nil
}
