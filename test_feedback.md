# 用户反馈功能测试文档

## 功能概述
已成功实现用户反馈功能，包含以下特性：
- 用户可以提交反馈
- 用户可以查看自己的反馈历史
- 管理员可以查看所有反馈列表
- 管理员可以查看反馈详情
- 管理员可以更新反馈状态和回复

## API 接口

### 用户端接口

#### 1. 提交反馈
- **接口**: `POST /api/v1/feedback`
- **需要登录**: 是
- **请求参数**:
```json
{
  "content": "反馈内容",
  "logOssUrl": "日志文件OSS地址（可选）",
  "contactInfo": "联系方式（可选）",
  "deviceInfo": "设备信息（可选）",
  "appVersion": "应用版本（可选）"
}
```

#### 2. 获取用户反馈列表
- **接口**: `GET /api/v1/feedbacks`
- **需要登录**: 是
- **返回**: 用户的所有反馈记录

### 管理员接口

#### 1. 获取反馈列表
- **接口**: `GET /api/v1/admin/feedbacks`
- **需要管理员权限**: 是
- **请求参数**:
```json
{
  "currentpage": 1,
  "pagesize": 10,
  "status": 0  // 可选，0=待处理，1=已处理，2=已关闭
}
```

#### 2. 获取反馈详情
- **接口**: `GET /api/v1/admin/feedback?id=反馈ID`
- **需要管理员权限**: 是

#### 3. 更新反馈状态
- **接口**: `PUT /api/v1/admin/feedback/status`
- **需要管理员权限**: 是
- **请求参数**:
```json
{
  "id": "反馈ID",
  "status": 1,  // 0=待处理，1=已处理，2=已关闭
  "adminReply": "管理员回复内容（可选）"
}
```

## 数据库表结构

### user_feedbacks 表
- `id`: 主键（雪花ID）
- `uid`: 用户ID
- `content`: 反馈内容
- `log_oss_url`: 日志OSS地址
- `contact_info`: 联系方式
- `status`: 状态（0=待处理，1=已处理，2=已关闭）
- `admin_reply`: 管理员回复
- `device_info`: 设备信息
- `app_version`: 应用版本
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 测试建议

1. **功能测试**:
   - 测试用户提交反馈
   - 测试用户查看反馈历史
   - 测试管理员查看反馈列表
   - 测试管理员更新反馈状态

2. **权限测试**:
   - 验证未登录用户无法访问反馈接口
   - 验证普通用户无法访问管理员接口

3. **数据验证**:
   - 测试必填字段验证
   - 测试数据格式验证

## 部署说明

1. 数据库迁移会自动创建 `user_feedbacks` 表
2. 所有依赖注入已配置完成
3. 路由已注册完成
4. 可以直接启动服务进行测试
