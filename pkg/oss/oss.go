package oss

import (
	"context"
	"fmt"
	"loop/internal/client"
	"net/url"
	"strings"
	"time"

	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/sirupsen/logrus"
)

// GetOssSignedURL 获取预签名的下载URL
func GetOssSignedURL(client *client.Client, url string) string {
	bucket, key, err := parseOSSURL(url)
	logrus.Println("bucket", bucket, "key", key)
	if err != nil {
		logrus.Println("parseOSSURL error", err)
		return url
	}
	result, err := client.OssClient.Presign(context.TODO(), &oss.GetObjectRequest{
		Bucket: oss.Ptr(bucket),
		Key:    oss.Ptr(key),
	},
		oss.PresignExpires(10*time.Minute),
	)
	if err != nil {
		logrus.Println("Presign error", err)
		return url
	}
	fmt.Printf("result.URL: %v\n", result.URL)
	return result.URL
}

// ParseOSSURL 从标准OSS URL中提取Bucket和Key
// 输入示例: https://bucket-name.oss-cn-hangzhou.aliyuncs.com/path/to/object.txt
// 输出: bucket="bucket-name", key="path/to/object.txt"
func parseOSSURL(ossURL string) (bucket, key string, err error) {

	// 解析URL结构
	u, err := url.Parse(ossURL)
	if err != nil {
		logrus.Printf("URL 解析失败: %v", err)
		return "", "", fmt.Errorf("invalid URL format: %v", err)
	}

	// 提取Bucket（域名第一段）
	hostParts := strings.Split(u.Host, ".")

	// 检查是否是标准的 OSS 域名格式
	// 标准格式为: bucket-name.oss-region.aliyuncs.com
	if len(hostParts) < 3 {
		logrus.Printf("域名部分切分数量不足: %d", len(hostParts))
		return "", "", fmt.Errorf("not a standard OSS endpoint")
	}

	// 获取 bucket 名称
	bucket = hostParts[0]

	// 提取Key（去除路径开头的/）
	key = strings.TrimPrefix(u.Path, "/")

	if bucket == "" || key == "" {
		logrus.Printf("提取结果无效: bucket=%s, key=%s", bucket, key)
		return "", "", fmt.Errorf("invalid bucket or key")
	}

	return bucket, key, nil
}

// DeleteOssObject 删除 OSS 文件
func DeleteOssObject(client *client.Client, url string) error {
	bucket, key, err := parseOSSURL(url)
	if err != nil {
		logrus.Println("parseOSSURL error", err)
		return err
	}
	_, err = client.OssClient.DeleteObject(context.TODO(), &oss.DeleteObjectRequest{
		Bucket: oss.Ptr(bucket),
		Key:    oss.Ptr(key),
	})
	if err != nil {
		logrus.Println("DeleteObject error", err)
		return err
	}
	return nil
}

// BatchDeleteOssObjects 批量删除 OSS 文件
// urls: 需要删除的 OSS 文件的完整URL列表
// 返回: 成功删除的数量，失败的数量，和错误信息（如有）
func BatchDeleteOssObjects(client *client.Client, urls []string) (int, int, error) {
	if len(urls) == 0 {
		return 0, 0, nil
	}

	// 解析第一个URL，确定bucket
	bucket, _, err := parseOSSURL(urls[0])
	if err != nil {
		logrus.Error("parse first url error", err)
		return 0, len(urls), err
	}

	var objects []oss.DeleteObject
	for _, u := range urls {
		b, k, err := parseOSSURL(u)
		if err != nil {
			logrus.Error("parseOSSURL error", err)
			continue
		}
		if b != bucket {
			logrus.Errorf("所有对象必须在同一个bucket: %s != %s", b, bucket)
			continue
		}
		objects = append(objects, oss.DeleteObject{Key: oss.Ptr(k)})
	}

	if len(objects) == 0 {
		return 0, len(urls), fmt.Errorf("无有效key可删除")
	}

	request := &oss.DeleteMultipleObjectsRequest{
		Bucket:  oss.Ptr(bucket),
		Objects: objects,
	}
	_, err = client.OssClient.DeleteMultipleObjects(context.TODO(), request)
	if err != nil {
		logrus.Error("Batch DeleteMultipleObjects error", err)
		return 0, len(objects), err
	}
	return len(objects), 0, nil
}
