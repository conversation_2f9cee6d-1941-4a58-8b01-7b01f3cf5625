package data

import (
	"encoding/json"
	"fmt"
	"loop/internal/config"
	"loop/internal/data/util"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/enum"
	"loop/pkg/jwtx"
	"loop/pkg/web"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
)

func NewDataCenterRepo(
	model *model.DataCenterModel,
	videoModel *model.VideoModel,
	resourceModel *model.ResourceModel,
	userRepo *UserRepo,
	config *config.Config,
) *DataCenterRepo {
	return &DataCenterRepo{
		model:         model,
		userRepo:      userRepo,
		videoModel:    videoModel,
		resourceModel: resourceModel,
		config:        config,
		service:       util.NewDataCenterService(videoModel, resourceModel),
	}
}

type DataCenterRepo struct {
	model         *model.DataCenterModel
	userRepo      *UserRepo
	videoModel    *model.VideoModel
	resourceModel *model.ResourceModel
	config        *config.Config
	service       *util.DataCenterService
}

func (s *DataCenterRepo) DataEpisodeUpdate(c *gin.Context, req request.DataEpisodeModLsTimeReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	dataEpisode, err := s.model.GetDataEpisode(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if dataEpisode != nil {
		dataEpisode.CurrentLsTimes = req.CurrentLsTimes
		if err := s.model.Update(dataEpisode, " id = ?", dataEpisode.Id); err != nil {
			return web.JsonInternalError(err)
		}
	}

	return web.JsonOK()
}

// DataEpisodeEachAdd 添加学习数据记录（重构版本，支持新旧数据格式）
func (s *DataCenterRepo) DataEpisodeEachAdd(c *gin.Context, req request.DataEpisodeEachReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 使用策略模式处理数据
	return s.processLearningData(c, uid, &req, "legacy")
}

// DataEpisodeEachAddEnhanced 添加增强学习数据记录（支持新数据格式）
func (s *DataCenterRepo) DataEpisodeEachAddEnhanced(c *gin.Context, uploadData map[string]interface{}) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 解析新的数据格式
	var learningData util.LearningDataUpload
	if err := json.Unmarshal([]byte(fmt.Sprintf("%v", uploadData)), &learningData); err != nil {
		return web.JsonError("数据格式解析失败: " + err.Error())
	}

	// 使用策略模式处理数据
	return s.processLearningData(c, uid, &learningData, "enhanced")
}

// processLearningData 使用策略模式处理学习数据
func (s *DataCenterRepo) processLearningData(c *gin.Context, uid string, data interface{}, processorType string) *web.JsonResult {
	// 创建数据完整性检查器
	checker := util.NewDataIntegrityChecker()

	// 创建数据处理器
	processor := util.NewLearningDataProcessor(processorType)

	// 数据验证
	if err := processor.Validate(data); err != nil {
		logrus.WithFields(logrus.Fields{
			"uid":            uid,
			"processor_type": processorType,
			"error":          err.Error(),
		}).Error("学习数据验证失败")
		return web.JsonError("数据验证失败: " + err.Error())
	}

	// 额外的完整性检查
	if processorType == "enhanced" {
		if uploadData, ok := data.(*util.LearningDataUpload); ok {
			if err := checker.CheckLearningDataUpload(uploadData); err != nil {
				return web.JsonError("数据完整性检查失败: " + err.Error())
			}
		}
	} else if processorType == "legacy" {
		if req, ok := data.(*request.DataEpisodeEachReq); ok {
			if err := checker.CheckLegacyRequest(req); err != nil {
				return web.JsonError("数据完整性检查失败: " + err.Error())
			}
		}
	}

	// 验证资源访问权限
	var resourceId string
	var resourceType int

	if processorType == "enhanced" {
		if uploadData, ok := data.(*util.LearningDataUpload); ok {
			resourceId = uploadData.SessionData.ResourceId
			resourceType = uploadData.SessionData.ResourceType
		}
	} else {
		if req, ok := data.(*request.DataEpisodeEachReq); ok {
			resourceId = req.ResourceId
			resourceType = req.ResourceType
		}
	}

	if err := s.validateResourceAccess(uid, resourceId, resourceType); err != nil {
		return web.JsonError("资源访问验证失败: " + err.Error())
	}

	// 处理数据
	result, err := processor.Process(uid, data)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"uid":            uid,
			"processor_type": processorType,
			"error":          err.Error(),
		}).Error("学习数据处理失败")
		return web.JsonInternalError(err)
	}

	// 开始事务处理
	return s.saveDataWithTransaction(uid, result)
}

// validateResourceAccess 验证用户对资源的访问权限
func (s *DataCenterRepo) validateResourceAccess(uid, resourceId string, resourceType int) error {
	if resourceType == int(enum.LocalResource) {
		localResource, err := s.videoModel.GetUserLocalResourceById(resourceId)
		if err != nil {
			return fmt.Errorf("获取本地资源失败: %w", err)
		}
		if localResource == nil {
			return fmt.Errorf("本地资源不存在")
		}
		// 可以添加更多的权限检查，比如检查资源是否属于该用户
	}
	// 对于远程资源，可以添加相应的验证逻辑
	return nil
}

// saveDataWithTransaction 使用事务保存数据
func (s *DataCenterRepo) saveDataWithTransaction(uid string, result *util.ProcessResult) *web.JsonResult {
	// 开始数据库事务
	tx := s.model.Begin()
	if tx.Error != nil {
		return web.JsonInternalError(tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logrus.WithFields(logrus.Fields{
				"uid":   uid,
				"error": r,
			}).Error("事务处理发生panic，已回滚")
		}
	}()

	// 保存 DataEpisodeEach 记录
	if err := tx.Create(result.DataEpisodeEach).Error; err != nil {
		tx.Rollback()
		logrus.WithFields(logrus.Fields{
			"uid":   uid,
			"error": err.Error(),
		}).Error("保存DataEpisodeEach记录失败")
		return web.JsonInternalError(err)
	}

	// 更新或创建 DataEpisode 记录
	dataEpisode, err := s.model.GetDataEpisode(uid, result.DataEpisodeEach.ResourceId, result.DataEpisodeEach.ResourceType)
	if err != nil {
		tx.Rollback()
		return web.JsonInternalError(err)
	}

	if dataEpisode != nil {
		// 更新现有记录
		dataEpisode.CurrentLsTimes = result.DataEpisodeEach.CurrentLsTimes

		// 检查是否完成了所有LS遍数
		if result.DataEpisodeEach.CurrentLsTimes >= dataEpisode.TargetLsTimes {
			// 计算完成时的总学习时长
			singleDataEpisodeResult, err := s.model.GetTotalDataEpisode(uid, dataEpisode.ResourceId, dataEpisode.ResourceType)
			if err != nil {
				tx.Rollback()
				return web.JsonInternalError(err)
			}

			dataEpisode.LearnDurationWhenFinish = singleDataEpisodeResult.TotalLearnDuration
			dataEpisode.EndTime = time.Now().Unix()
			dataEpisode.Status = 1

			logrus.WithFields(logrus.Fields{
				"uid":         uid,
				"resource_id": dataEpisode.ResourceId,
				"ls_times":    result.DataEpisodeEach.CurrentLsTimes,
				"duration":    dataEpisode.LearnDurationWhenFinish,
			}).Info("用户完成了资源的所有LS学习")
		}

		if err := tx.Save(dataEpisode).Error; err != nil {
			tx.Rollback()
			logrus.WithFields(logrus.Fields{
				"uid":   uid,
				"error": err.Error(),
			}).Error("更新DataEpisode记录失败")
			return web.JsonInternalError(err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logrus.WithFields(logrus.Fields{
			"uid":   uid,
			"error": err.Error(),
		}).Error("事务提交失败")
		return web.JsonInternalError(err)
	}

	// 记录成功日志
	logrus.WithFields(logrus.Fields{
		"uid":            uid,
		"resource_id":    result.DataEpisodeEach.ResourceId,
		"resource_type":  result.DataEpisodeEach.ResourceType,
		"ls_times":       result.DataEpisodeEach.CurrentLsTimes,
		"learn_duration": result.DataEpisodeEach.LearnDuration,
		"processor_type": result.Metadata["processor_type"],
	}).Info("学习数据保存成功")

	return web.JsonOK()
}
func (s *DataCenterRepo) GetEpisodeLsData(c *gin.Context, req request.EpisodeLsDataReq) *web.JsonResult {
	uid := jwtx.GetUid(c)

	// 使用服务类获取资源名称
	episodeName, err := s.service.GetResourceName(req.ResourceId, req.ResourceType)
	if err != nil {
		return s.service.HandleError(err, map[string]interface{}{
			"uid":           uid,
			"resource_id":   req.ResourceId,
			"resource_type": req.ResourceType,
		}, "获取资源名称失败")
	}
	dataEpisode, err := s.model.GetDataEpisode(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if dataEpisode == nil {
		return web.JsonEntityNotFound(c)
	}
	dpisodeLsDataList, err := s.model.GetEpisodeLsData(uid, req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}

	// Create a map for easy lookup of existing data by LsTimes
	existingDataMap := make(map[int]response.EpisodeLsData)
	for _, data := range dpisodeLsDataList {
		existingDataMap[data.LsTimes] = data
	}

	// Fill in missing data entries up to CurrentLsTimes
	var completeDataList []response.EpisodeLsData
	currentLsTimes := int(dataEpisode.CurrentLsTimes)
	for i := 1; i <= currentLsTimes; i++ {
		if data, exists := existingDataMap[i]; exists {
			completeDataList = append(completeDataList, data)
		} else {
			// Add placeholder for missing data
			completeDataList = append(completeDataList, response.EpisodeLsData{
				LsTimes:            i,
				TotalLearnDuration: 0,
				FinishTime:         0,
			})
		}
	}
	// Sort the completeDataList by LsTimes in descending order
	sort.Slice(completeDataList, func(i, j int) bool {
		return completeDataList[i].LsTimes > completeDataList[j].LsTimes
	})

	var episodesResp response.DataEpisodeResp
	copier.Copy(&episodesResp, &dataEpisode)

	singleDataEpisodeResult, err := s.model.GetTotalDataEpisode(uid, dataEpisode.ResourceId, dataEpisode.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if singleDataEpisodeResult != nil {
		episodesResp.TotalLearnDuration = singleDataEpisodeResult.TotalLearnDuration
		episodesResp.TotalLearnDayTimes = singleDataEpisodeResult.DayCount
	}

	episodesResp.EpisodeName = episodeName
	return web.JsonData(response.EpisodeLsDataResp{
		Episodes:      episodesResp,
		EpisodeLsData: completeDataList,
	})

}

func (s *DataCenterRepo) DataEpisodeHome(c *gin.Context, req request.DataEpisodeTypeReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	dataEpisodeList, err := s.model.GetDataEpisodeList(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if len(dataEpisodeList) == 0 {
		//如果当前没有学习的 就返回空的数据
		return web.JsonEmptyData()
	}
	var dailyResps []response.DataEpisodeDailyTodayResp
	var episodesResps []response.DataEpisodeResp //剧集列表数据
	for _, dataEpisode := range dataEpisodeList {
		var episodeName = ""
		if dataEpisode.ResourceType == int(enum.LocalResource) {
			localResource, err := s.videoModel.GetUserLocalResourceById(dataEpisode.ResourceId)
			if err != nil {
				return web.JsonInternalError(err)
			}
			if localResource != nil {
				episodeName = localResource.FileName
			}
		} else if dataEpisode.ResourceType == int(enum.RemoteResouce) {
			resourceRelation, err := s.resourceModel.GetOriginResourceRelation(dataEpisode.ResourceId)
			if err != nil {
				return web.JsonInternalError(err)
			}
			if resourceRelation == nil {
				return web.JsonEntityNotFound(c)
			}
			episodeName = resourceRelation.Title
		}
		if dataEpisode.Status == 0 && req.Type == 1 {
			//学习中的数据
			//获取指定时间戳对应的当天的所有 DataEpisodeDaily
			todayDataEpisodeDailys, err := s.model.GetTodayDataEpisodeDailys(uid, dataEpisode.ResourceId, dataEpisode.ResourceType)
			if err != nil {
				return web.JsonInternalError(err)
			}
			if len(todayDataEpisodeDailys) != 0 {
				var dataEpisodeDailyResp response.DataEpisodeDailyTodayResp
				copier.Copy(&dataEpisodeDailyResp, &dataEpisode)
				dataEpisodeDailyResp.EpisodeName = episodeName
				dataEpisodeDailyResp.TargetLsTimes = dataEpisode.TargetLsTimes
				duration, err := s.calculateTotalLearnDuration(todayDataEpisodeDailys)
				if err != nil {
					dataEpisodeDailyResp.TotalLearnDuration = 0
				} else {
					dataEpisodeDailyResp.TotalLearnDuration = duration
				}
				dailyResps = append(dailyResps, dataEpisodeDailyResp)
			}
		}

		var episodesResp response.DataEpisodeResp
		copier.Copy(&episodesResp, &dataEpisode)

		startTime, endTime := s.getStartEndTime(req.Type, req.StartTime, req.EndTime)
		if req.Type == 4 {
			startOfYear := time.Date(startTime.Year(), 1, 1, 0, 0, 0, 0, startTime.Location())
			endOfYear := time.Date(endTime.Year()+1, 1, 1, 0, 0, 0, 0, startTime.Location())
			startTime = startOfYear
			endTime = endOfYear
		}
		singleDataEpisodeResult, err := s.model.GetTotalDataEpisodeByTime(uid, dataEpisode.ResourceId, dataEpisode.ResourceType, &startTime, &endTime)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if singleDataEpisodeResult != nil {
			episodesResp.TotalLearnDuration = singleDataEpisodeResult.TotalLearnDuration
			episodesResp.TotalLearnDayTimes = singleDataEpisodeResult.DayCount
		}
		episodesResp.EpisodeName = episodeName
		episodesResp.TargetDesc = "没文案"

		episodesResps = append(episodesResps, episodesResp)
	}

	dataEpisodeTotalResp := response.DataEpisodeTotalResp{}
	totalDataEpisodeResult, err := s.model.GetTotalDataEpisode(uid, "", 0)
	if err != nil {
		return web.JsonInternalError(err)
	}

	if totalDataEpisodeResult != nil {
		dataEpisodeTotalResp.TotalLearnDuration = totalDataEpisodeResult.TotalLearnDuration
		dataEpisodeTotalResp.TotalLearnDayTimes = totalDataEpisodeResult.DayCount
		dataEpisodeTotalResp.TotalLearnVideoSize = len(dataEpisodeList)
	}

	resp := &response.DataEpisodeHomeResp{
		Daily:    &dailyResps,
		Total:    &dataEpisodeTotalResp,
		Episodes: &episodesResps,
	}
	return web.JsonData(resp)
}

func (s *DataCenterRepo) DataEpisodeList(c *gin.Context, req request.EmptyReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	dataEpisodeList, err := s.model.GetDataEpisodeList(uid)
	if err != nil {
		return web.JsonInternalError(err)
	}
	if len(dataEpisodeList) == 0 {
		//如果当前没有学习的 就返回空的数据
		return web.JsonEmptyData()
	}
	var episodesResps []response.DataEpisodeResp //剧集列表数据
	for _, dataEpisode := range dataEpisodeList {
		var episodeName = ""
		if dataEpisode.ResourceType == int(enum.LocalResource) {
			localResource, err := s.videoModel.GetUserLocalResourceById(dataEpisode.ResourceId)
			if err != nil {
				return web.JsonInternalError(err)
			}
			if localResource != nil {
				episodeName = localResource.FileName
			}
		} else if dataEpisode.ResourceType == int(enum.RemoteResouce) {
			resourceRelation, err := s.resourceModel.GetOriginResourceRelation(dataEpisode.ResourceId)
			if err != nil {
				return web.JsonInternalError(err)
			}
			if resourceRelation == nil {
				return web.JsonEntityNotFound(c)
			}
			episodeName = resourceRelation.Title
		}

		var episodesResp response.DataEpisodeResp
		copier.Copy(&episodesResp, &dataEpisode)
		totalDataEpisodeResult, err := s.model.GetTotalDataEpisode(uid, dataEpisode.ResourceId, dataEpisode.ResourceType)
		if err != nil {
			return web.JsonInternalError(err)
		}
		if totalDataEpisodeResult != nil {
			episodesResp.TotalLearnDuration = totalDataEpisodeResult.TotalLearnDuration
			episodesResp.TotalLearnDayTimes = totalDataEpisodeResult.DayCount
		}
		episodesResp.EpisodeName = episodeName
		episodesResp.TargetDesc = "没文案"

		episodesResps = append(episodesResps, episodesResp)
	}
	return web.JsonData(episodesResps)
}
func (s *DataCenterRepo) calculateTotalLearnDuration(todayDataEpisode []*model.DataEpisodeEach) (int64, error) {
	// 使用服务类计算时长
	return s.service.CalculateDuration(todayDataEpisode), nil
}

func (s *DataCenterRepo) DataEpisodeByChart(c *gin.Context, req request.DataEpisodeTypeReq) *web.JsonResult {
	if req.Type == 1 {
		return s.DataEpisodeDay(c, req.StartTime, req.EndTime)
	}
	if req.Type == 2 {
		return s.DataEpisodeWeek(c, req.StartTime, req.EndTime)
	}
	if req.Type == 3 {
		return s.DataEpisodeMonth(c, req.StartTime, req.EndTime)
	}
	if req.Type == 4 {
		return s.DataEpisodeYear(c, req.StartTime, req.EndTime)
	}
	return web.JsonEmptyData()

}

func (s *DataCenterRepo) DataEpisodeDay(c *gin.Context, startTime int64, endTime int64) *web.JsonResult {
	uid := jwtx.GetUid(c)
	var totalLearnDuration int64
	hourlyDatas := make([]response.DataEpisodeChartSubResp, 24)
	startOfDay, endOfDay := s.getStartEndTime(1, startTime, endTime)
	dayData, err := s.model.GetDayDataEpisode(uid, startOfDay, endOfDay)
	if err != nil {
		return web.JsonInternalError(err)
	}

	for hourIndex := 0; hourIndex < 24; hourIndex++ {
		episodes := dayData[hourIndex]
		hourDuration, err := s.calculateTotalLearnDuration(episodes)
		if err != nil {
			return web.JsonInternalError(err)
		}
		totalLearnDuration += hourDuration

		currentHour := startOfDay.Add(time.Duration(hourIndex) * time.Hour)
		startDate := currentHour.Unix()
		endDate := currentHour.Add(time.Hour).Unix()

		hourlyDatas[hourIndex] = response.DataEpisodeChartSubResp{
			Duration:        hourDuration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: s.formatDateString(currentHour, true),
		}
	}
	response := response.DataEpisodeChartResp{
		TotalLearnDuration: totalLearnDuration,
		Datas:              hourlyDatas,
	}

	return web.JsonData(response)
}

func (s *DataCenterRepo) DataEpisodeWeek(c *gin.Context, startTime int64, endTime int64) *web.JsonResult {
	uid := jwtx.GetUid(c)

	var totalLearnDuration int64
	datas := make([]response.DataEpisodeChartSubResp, 7)
	startOfWeek, endOfWeek := s.getStartEndTime(1, startTime, endTime)
	weekData, err := s.model.GetWeekDataEpisode(uid, startOfWeek, endOfWeek)
	if err != nil {
		return web.JsonInternalError(err)
	}

	for dayIndex := 0; dayIndex < 7; dayIndex++ {
		episodes := weekData[dayIndex]
		dayDuration, err := s.calculateTotalLearnDuration(episodes)
		if err != nil {
			return web.JsonInternalError(err)
		}
		totalLearnDuration += dayDuration

		currentDay := startOfWeek.AddDate(0, 0, dayIndex)
		startDate := time.Date(currentDay.Year(), currentDay.Month(), currentDay.Day(), 0, 0, 0, 0, currentDay.Location()).Unix()
		endDate := time.Date(currentDay.Year(), currentDay.Month(), currentDay.Day(), 23, 59, 59, 999999999, currentDay.Location()).Unix()

		datas[dayIndex] = response.DataEpisodeChartSubResp{
			Duration:        dayDuration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: s.formatDateString(currentDay, false),
			EndDateString:   s.formatDateString(currentDay, false),
		}
	}
	response := response.DataEpisodeChartResp{
		TotalLearnDuration: totalLearnDuration,
		Datas:              datas,
	}

	return web.JsonData(response)
}

func (s *DataCenterRepo) DataEpisodeMonth(c *gin.Context, startTime int64, endTime int64) *web.JsonResult {
	uid := jwtx.GetUid(c)
	start, end := s.getStartEndTime(3, startTime, endTime)
	daysInMonth := time.Date(start.Year(), start.Month()+1, 0, 0, 0, 0, 0, start.Location()).Day()
	dailyDatas := make([]response.DataEpisodeChartSubResp, daysInMonth)
	var totalLearnDuration int64
	monthData, err := s.model.GetMonthDataEpisode(uid, start, end)
	if err != nil {
		return web.JsonInternalError(err)
	}
	for dayIndex := 0; dayIndex < daysInMonth; dayIndex++ {
		episodes := monthData[dayIndex+1]
		dayDuration, err := s.calculateTotalLearnDuration(episodes)
		if err != nil {
			return web.JsonInternalError(err)
		}
		totalLearnDuration += dayDuration

		currentDay := time.Date(start.Year(), start.Month(), dayIndex+1, 0, 0, 0, 0, start.Location())
		startDate := time.Date(currentDay.Year(), currentDay.Month(), currentDay.Day(), 0, 0, 0, 0, currentDay.Location()).Unix()
		endDate := time.Date(currentDay.Year(), currentDay.Month(), currentDay.Day(), 23, 59, 59, 999999999, currentDay.Location()).Unix()

		dailyDatas[dayIndex] = response.DataEpisodeChartSubResp{
			Duration:        dayDuration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: s.formatDateString(currentDay, false),
			EndDateString:   s.formatDateString(currentDay, false),
		}
	}

	response := response.DataEpisodeChartResp{
		TotalLearnDuration: totalLearnDuration,
		Datas:              dailyDatas,
	}

	return web.JsonData(response)
}
func (s *DataCenterRepo) DataEpisodeYear(c *gin.Context, startTime int64, endTime int64) *web.JsonResult {
	uid := jwtx.GetUid(c)

	monthlyDatas := make([]response.DataEpisodeChartSubResp, 12)
	var totalLearnDuration int64
	start, end := s.getStartEndTime(4, startTime, endTime)
	startOfYear := time.Date(start.Year(), 1, 1, 0, 0, 0, 0, start.Location())
	endOfYear := time.Date(end.Year()+1, 1, 1, 0, 0, 0, 0, start.Location())

	yearData, err := s.model.GetYearDataEpisode(uid, startOfYear, endOfYear)
	if err != nil {
		return web.JsonInternalError(err)
	}

	for monthIndex := 0; monthIndex < 12; monthIndex++ {
		episodes := yearData[monthIndex+1]
		monthDuration, err := s.calculateTotalLearnDuration(episodes)
		if err != nil {
			return web.JsonInternalError(err)
		}
		totalLearnDuration += monthDuration

		currentMonth := time.Date(start.Year(), time.Month(monthIndex+1), 1, 0, 0, 0, 0, start.Location())
		startDate := time.Date(currentMonth.Year(), currentMonth.Month(), 1, 0, 0, 0, 0, currentMonth.Location()).Unix()
		endDate := time.Date(currentMonth.Year(), currentMonth.Month()+1, 0, 23, 59, 59, 999999999, currentMonth.Location()).Unix()

		monthlyDatas[monthIndex] = response.DataEpisodeChartSubResp{
			Duration:        monthDuration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: s.formatDateString(currentMonth, false),
			EndDateString:   s.formatDateString(currentMonth.AddDate(0, 1, -1), false),
		}
	}

	response := response.DataEpisodeChartResp{
		TotalLearnDuration: totalLearnDuration,
		Datas:              monthlyDatas,
	}

	return web.JsonData(response)
}

func (s *DataCenterRepo) formatDateString(t time.Time, isDayData bool) string {
	// 使用服务类格式化日期
	return s.service.FormatDate(t, isDayData)
}
func (s *DataCenterRepo) getStartEndTime(timeType int, startTime int64, endTime int64) (time.Time, time.Time) {
	// 使用服务类计算时间范围
	return s.service.GetTimeRange(timeType, startTime, endTime)
}
