package data

import (
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/jwtx"
	"loop/pkg/web"
	"strings"

	"loop/pkg/oss"

	"github.com/gin-gonic/gin"
)

type SpeechEvaluationData struct {
	model *model.SpeechEvaluationModel
}

func NewSpeechEvaluationData(model *model.SpeechEvaluationModel) *SpeechEvaluationData {
	return &SpeechEvaluationData{model: model}
}

func (d *SpeechEvaluationData) Create(evaluation *model.SpeechEvaluation) error {
	return d.model.Create(evaluation)
}

func (d *SpeechEvaluationData) GetByResource(resourceId string, resourceType int) ([]*model.SpeechEvaluation, error) {
	return d.model.GetEvaluationsByResource(resourceId, resourceType)
}

func (d *SpeechEvaluationData) Update(evaluation *model.SpeechEvaluation) error {
	return d.model.Update(evaluation)
}

func (d *SpeechEvaluationData) CreateFromReq(c *gin.Context, req request.SpeechEvaluationReq) *web.JsonResult {
	eval := &model.SpeechEvaluation{
		Uid:          jwtx.GetUid(c),
		ResourceId:   req.ResourceId,
		ResourceType: req.ResourceType,
		Content:      req.Content,
		AudioUrl:     req.AudioUrl,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
	}
	err := d.model.Create(eval)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(eval)
}

func (d *SpeechEvaluationData) ListByReq(c *gin.Context, req request.SpeechEvaluationListReq) *web.JsonResult {
	list, err := d.model.GetEvaluationsByResource(req.ResourceId, req.ResourceType)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(list)
}

func (d *SpeechEvaluationData) UpdateFromReq(c *gin.Context, req request.SpeechEvaluationUpdateReq) *web.JsonResult {
	eval := &model.SpeechEvaluation{
		ModelAutoId:  model.ModelAutoId{Id: req.Id},
		Uid:          jwtx.GetUid(c),
		ResourceId:   req.ResourceId,
		ResourceType: req.ResourceType,
		Content:      req.Content,
		AudioUrl:     req.AudioUrl,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
	}

	// 查询原始记录，判断是否需要删除 oss 上的旧音频
	var oldEval model.SpeechEvaluation
	if found, err := d.model.GetOne(&oldEval, "id = ?", req.Id); found && err == nil {
		if oldEval.AudioUrl != "" && req.AudioUrl != "" && oldEval.AudioUrl != req.AudioUrl && strings.HasPrefix(oldEval.AudioUrl, "http") {
			_ = oss.DeleteOssObject(nil, oldEval.AudioUrl)
		}
	}

	err := d.model.Update(eval)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(eval)
}

// BatchCreateFromReq 批量创建语音评测记录
func (d *SpeechEvaluationData) BatchCreateFromReq(c *gin.Context, req request.SpeechEvaluationBatchReq) *web.JsonResult {
	if len(req.Evaluations) == 0 {
		return web.JsonError("评测数据不能为空")
	}

	uid := jwtx.GetUid(c)
	var evaluations []*model.SpeechEvaluation

	// 转换请求数据为模型数据
	for _, evalReq := range req.Evaluations {
		eval := &model.SpeechEvaluation{
			Uid:          uid,
			ResourceId:   evalReq.ResourceId,
			ResourceType: evalReq.ResourceType,
			Content:      evalReq.Content,
			AudioUrl:     evalReq.AudioUrl,
			StartTime:    evalReq.StartTime,
			EndTime:      evalReq.EndTime,
		}
		evaluations = append(evaluations, eval)
	}

	// 批量创建
	err := d.model.CreateBatch(evaluations)
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(evaluations)
}

// BatchUpdateFromReq 批量更新语音评测记录
func (d *SpeechEvaluationData) BatchUpdateFromReq(c *gin.Context, req request.SpeechEvaluationBatchUpdateReq) *web.JsonResult {
	if len(req.Evaluations) == 0 {
		return web.JsonError("评测数据不能为空")
	}

	uid := jwtx.GetUid(c)
	var evaluations []*model.SpeechEvaluation

	// 转换请求数据为模型数据
	for _, evalReq := range req.Evaluations {
		eval := &model.SpeechEvaluation{
			ModelAutoId:  model.ModelAutoId{Id: evalReq.Id},
			Uid:          uid,
			ResourceId:   evalReq.ResourceId,
			ResourceType: evalReq.ResourceType,
			Content:      evalReq.Content,
			AudioUrl:     evalReq.AudioUrl,
			StartTime:    evalReq.StartTime,
			EndTime:      evalReq.EndTime,
		}
		evaluations = append(evaluations, eval)
	}

	// 批量更新
	err := d.model.UpdateBatch(evaluations)
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(evaluations)
}

// BatchUpdateAudioUrlFromReq 批量更新AudioUrl
func (d *SpeechEvaluationData) BatchUpdateAudioUrlFromReq(c *gin.Context, req request.SpeechEvaluationBatchUpdateAudioUrlReq) *web.JsonResult {
	if len(req.Evaluations) == 0 {
		return web.JsonError("评测数据不能为空")
	}

	uid := jwtx.GetUid(c)
	var evaluations []*model.SpeechEvaluation

	// 只根据id和audioUrl构造SpeechEvaluation
	for _, evalReq := range req.Evaluations {
		eval := &model.SpeechEvaluation{
			ModelAutoId: model.ModelAutoId{Id: evalReq.Id},
			AudioUrl:    evalReq.AudioUrl,
		}
		evaluations = append(evaluations, eval)
	}

	// 批量更新AudioUrl
	err := d.model.UpdateBatchAudioUrl(uid, evaluations)
	if err != nil {
		return web.JsonInternalError(err)
	}

	return web.JsonData(evaluations)
}
