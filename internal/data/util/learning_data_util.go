package util

import (
	"encoding/json"
	"errors"
	"fmt"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/util"
	"loop/pkg/web"
	"time"

	"github.com/sirupsen/logrus"
)

// LearningDataProcessor 学习数据处理策略接口
type LearningDataProcessor interface {
	Validate(data interface{}) error
	Process(uid string, data interface{}) (*ProcessResult, error)
	GetProcessorType() string
}

// ProcessResult 处理结果
type ProcessResult struct {
	DataEpisodeEach *model.DataEpisodeEach
	DataEpisode     *model.DataEpisode
	SentencesJSON   string
	Metadata        map[string]interface{}
}

// LearningSessionData 前端传递的学习会话数据结构
type LearningSessionData struct {
	ResourceId           string `json:"resourceId"`
	ResourceType         int    `json:"resourceType"`
	SessionStartTime     int64  `json:"sessionStartTime"`
	SessionEndTime       int64  `json:"sessionEndTime"`
	TotalSessionDuration int64  `json:"totalSessionDuration"` // 毫秒
	TotalPlayDuration    int64  `json:"totalPlayDuration"`    // 毫秒
	TotalRecordDuration  int64  `json:"totalRecordDuration"`  // 毫秒
	LsTimes              int64  `json:"lsTimes"`
}

// SentenceLearningRecord 句子学习记录
type SentenceLearningRecord struct {
	SubtitleId      string `json:"subtitleId"`
	TargetPassTimes int64  `json:"targetPassTimes"`
	LearnDuration   int64  `json:"learnDuration"`
	LearnTimes      int64  `json:"learnTimes"`
	SubtitleIndex   int64  `json:"subtitleIndex"`
}

// LearningDataUpload 前端上传的完整学习数据
type LearningDataUpload struct {
	SessionData     LearningSessionData      `json:"sessionData"`
	SentenceRecords []SentenceLearningRecord `json:"sentenceRecords"`
}

// NewLearningDataProcessor 工厂方法创建数据处理器
func NewLearningDataProcessor(processorType string) LearningDataProcessor {
	switch processorType {
	case "legacy":
		return &LegacyDataProcessor{}
	case "enhanced":
		return &EnhancedDataProcessor{}
	default:
		return &LegacyDataProcessor{} // 默认使用传统处理器
	}
}

// LegacyDataProcessor 传统数据处理器（兼容现有逻辑）
type LegacyDataProcessor struct{}

func (p *LegacyDataProcessor) GetProcessorType() string {
	return "legacy"
}

func (p *LegacyDataProcessor) Validate(data interface{}) error {
	req, ok := data.(*request.DataEpisodeEachReq)
	if !ok {
		return errors.New("invalid data type for legacy processor")
	}

	// 基础验证
	if !util.IsValidID(req.ResourceId) {
		return errors.New("invalid resource id")
	}

	if req.ResourceType <= 0 {
		return errors.New("invalid resource type")
	}

	if req.StartTime <= 0 || req.EndTime <= 0 {
		return errors.New("invalid time range")
	}

	if req.StartTime >= req.EndTime {
		return errors.New("start time must be before end time")
	}

	if req.LearnDuration <= 0 {
		return errors.New("learn duration must be positive")
	}

	return nil
}

func (p *LegacyDataProcessor) Process(uid string, data interface{}) (*ProcessResult, error) {
	req, ok := data.(*request.DataEpisodeEachReq)
	if !ok {
		return nil, errors.New("invalid data type for legacy processor")
	}

	// 处理句子数据
	var sentencesJSON string
	if len(req.SentenceList) > 0 {
		data, err := json.Marshal(req.SentenceList)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal sentence data: %w", err)
		}
		sentencesJSON = string(data)
	}

	// 创建 DataEpisodeEach 记录
	dataEpisodeEach := &model.DataEpisodeEach{
		Uid:            uid,
		ResourceId:     req.ResourceId,
		ResourceType:   req.ResourceType,
		LearnDuration:  req.LearnDuration,
		StartTime:      req.StartTime,
		EndTime:        req.EndTime,
		CurrentLsTimes: req.CurrentLsTimes,
		SentencesJSON:  sentencesJSON,
	}

	return &ProcessResult{
		DataEpisodeEach: dataEpisodeEach,
		SentencesJSON:   sentencesJSON,
		Metadata: map[string]interface{}{
			"processor_type": "legacy",
			"sentence_count": len(req.SentenceList),
		},
	}, nil
}

// EnhancedDataProcessor 增强数据处理器（支持新的数据结构）
type EnhancedDataProcessor struct{}

func (p *EnhancedDataProcessor) GetProcessorType() string {
	return "enhanced"
}

func (p *EnhancedDataProcessor) Validate(data interface{}) error {
	uploadData, ok := data.(*LearningDataUpload)
	if !ok {
		return errors.New("invalid data type for enhanced processor")
	}

	// 验证会话数据
	sessionData := uploadData.SessionData
	if !util.IsValidID(sessionData.ResourceId) {
		return errors.New("invalid resource id")
	}

	if sessionData.ResourceType <= 0 {
		return errors.New("invalid resource type")
	}

	if sessionData.SessionStartTime <= 0 || sessionData.SessionEndTime <= 0 {
		return errors.New("invalid session time range")
	}

	if sessionData.SessionStartTime >= sessionData.SessionEndTime {
		return errors.New("session start time must be before end time")
	}

	// 验证时长数据的合理性
	if sessionData.TotalSessionDuration <= 0 {
		return errors.New("total session duration must be positive")
	}

	// 播放时长和录音时长不能超过总会话时长
	if sessionData.TotalPlayDuration > sessionData.TotalSessionDuration {
		return errors.New("play duration cannot exceed session duration")
	}

	if sessionData.TotalRecordDuration > sessionData.TotalSessionDuration {
		return errors.New("record duration cannot exceed session duration")
	}

	if sessionData.LsTimes <= 0 {
		return errors.New("ls times must be positive")
	}

	// 验证句子记录
	for i, record := range uploadData.SentenceRecords {
		if record.SubtitleId == "" {
			return fmt.Errorf("sentence record %d: subtitle id is required", i)
		}
		if record.LearnDuration < 0 {
			return fmt.Errorf("sentence record %d: learn duration cannot be negative", i)
		}
		if record.LearnTimes < 0 {
			return fmt.Errorf("sentence record %d: learn times cannot be negative", i)
		}
	}

	return nil
}

func (p *EnhancedDataProcessor) Process(uid string, data interface{}) (*ProcessResult, error) {
	uploadData, ok := data.(*LearningDataUpload)
	if !ok {
		return nil, errors.New("invalid data type for enhanced processor")
	}

	sessionData := uploadData.SessionData

	// 转换句子记录为兼容格式
	var sentenceList []request.DataEpisodeSentence
	for _, record := range uploadData.SentenceRecords {
		sentenceList = append(sentenceList, request.DataEpisodeSentence{
			SubtitleId:      record.SubtitleId,
			TargetPassTimes: record.TargetPassTimes,
			LearnDuration:   record.LearnDuration,
			LearnTimes:      record.LearnTimes,
			SubtitleIndex:   record.SubtitleIndex,
		})
	}

	// 处理句子数据
	var sentencesJSON string
	if len(sentenceList) > 0 {
		data, err := json.Marshal(sentenceList)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal sentence data: %w", err)
		}
		sentencesJSON = string(data)
	}

	// 将毫秒转换为秒（数据库存储格式）
	learnDurationSeconds := sessionData.TotalSessionDuration / 1000

	// 创建 DataEpisodeEach 记录
	dataEpisodeEach := &model.DataEpisodeEach{
		Uid:            uid,
		ResourceId:     sessionData.ResourceId,
		ResourceType:   sessionData.ResourceType,
		LearnDuration:  learnDurationSeconds,
		StartTime:      sessionData.SessionStartTime,
		EndTime:        sessionData.SessionEndTime,
		CurrentLsTimes: sessionData.LsTimes,
		SentencesJSON:  sentencesJSON,
	}

	// 创建扩展元数据
	metadata := map[string]interface{}{
		"processor_type":         "enhanced",
		"sentence_count":         len(uploadData.SentenceRecords),
		"total_session_duration": sessionData.TotalSessionDuration,
		"total_play_duration":    sessionData.TotalPlayDuration,
		"total_record_duration":  sessionData.TotalRecordDuration,
		"play_duration_ratio":    float64(sessionData.TotalPlayDuration) / float64(sessionData.TotalSessionDuration),
		"record_duration_ratio":  float64(sessionData.TotalRecordDuration) / float64(sessionData.TotalSessionDuration),
	}

	logrus.WithFields(logrus.Fields{
		"uid":                    uid,
		"resource_id":            sessionData.ResourceId,
		"total_session_duration": sessionData.TotalSessionDuration,
		"total_play_duration":    sessionData.TotalPlayDuration,
		"total_record_duration":  sessionData.TotalRecordDuration,
		"sentence_count":         len(uploadData.SentenceRecords),
	}).Info("Enhanced learning data processed")

	return &ProcessResult{
		DataEpisodeEach: dataEpisodeEach,
		SentencesJSON:   sentencesJSON,
		Metadata:        metadata,
	}, nil
}

// DataValidator 数据验证工具类
type DataValidator struct{}

// ValidateTimeRange 验证时间范围的合理性
func (v *DataValidator) ValidateTimeRange(startTime, endTime int64) error {
	if startTime <= 0 || endTime <= 0 {
		return errors.New("时间戳必须为正数")
	}

	if startTime >= endTime {
		return errors.New("开始时间必须早于结束时间")
	}

	// 检查时间是否在合理范围内（不能是未来时间，不能太久远）
	now := time.Now().Unix()
	if startTime > now || endTime > now {
		return errors.New("时间不能是未来时间")
	}

	// 检查学习时长是否合理（不超过24小时）
	duration := endTime - startTime
	if duration > 24*3600 {
		return errors.New("单次学习时长不能超过24小时")
	}

	return nil
}

// ValidateDurationConsistency 验证时长数据的一致性
func (v *DataValidator) ValidateDurationConsistency(sessionData *LearningSessionData) error {
	// 计算实际会话时长
	actualDuration := (sessionData.SessionEndTime - sessionData.SessionStartTime) * 1000 // 转换为毫秒

	// 允许一定的误差范围（±10%）
	tolerance := float64(actualDuration) * 0.1
	minExpected := float64(actualDuration) - tolerance
	maxExpected := float64(actualDuration) + tolerance

	if float64(sessionData.TotalSessionDuration) < minExpected ||
		float64(sessionData.TotalSessionDuration) > maxExpected {
		return fmt.Errorf("会话时长不一致：实际时长 %d ms，报告时长 %d ms",
			actualDuration, sessionData.TotalSessionDuration)
	}

	return nil
}

// ValidateResourceAccess 验证用户对资源的访问权限
func (v *DataValidator) ValidateResourceAccess(uid, resourceId string, resourceType int) error {
	// 基础ID验证
	if !util.IsValidID(uid) {
		return errors.New("无效的用户ID")
	}

	if !util.IsValidID(resourceId) {
		return errors.New("无效的资源ID")
	}

	// 资源类型验证
	if resourceType != 1 && resourceType != 2 {
		return errors.New("无效的资源类型，必须是1（远程资源）或2（本地资源）")
	}

	return nil
}

// ValidateSentenceRecords 验证句子记录的完整性
func (v *DataValidator) ValidateSentenceRecords(records []SentenceLearningRecord) error {
	if len(records) == 0 {
		return nil // 允许空记录
	}

	// 检查重复的字幕索引
	indexMap := make(map[int64]bool)
	for i, record := range records {
		if record.SubtitleIndex < 0 {
			return fmt.Errorf("句子记录 %d：字幕索引不能为负数", i)
		}

		if indexMap[record.SubtitleIndex] {
			return fmt.Errorf("句子记录 %d：字幕索引 %d 重复", i, record.SubtitleIndex)
		}
		indexMap[record.SubtitleIndex] = true

		// 验证学习时长和次数的合理性
		if record.LearnDuration < 0 {
			return fmt.Errorf("句子记录 %d：学习时长不能为负数", i)
		}

		if record.LearnTimes < 0 {
			return fmt.Errorf("句子记录 %d：学习次数不能为负数", i)
		}

		if record.TargetPassTimes < 0 {
			return fmt.Errorf("句子记录 %d：目标通过次数不能为负数", i)
		}

		// 验证通过次数不能超过学习次数
		if record.TargetPassTimes > record.LearnTimes {
			return fmt.Errorf("句子记录 %d：目标通过次数不能超过学习次数", i)
		}
	}

	return nil
}

// DataIntegrityChecker 数据完整性检查器
type DataIntegrityChecker struct {
	validator *DataValidator
}

// NewDataIntegrityChecker 创建数据完整性检查器
func NewDataIntegrityChecker() *DataIntegrityChecker {
	return &DataIntegrityChecker{
		validator: &DataValidator{},
	}
}

// CheckLearningDataUpload 检查学习数据上传的完整性
func (c *DataIntegrityChecker) CheckLearningDataUpload(data *LearningDataUpload) error {
	// 验证会话数据
	if err := c.validator.ValidateTimeRange(data.SessionData.SessionStartTime, data.SessionData.SessionEndTime); err != nil {
		return fmt.Errorf("会话时间验证失败: %w", err)
	}

	if err := c.validator.ValidateDurationConsistency(&data.SessionData); err != nil {
		return fmt.Errorf("时长一致性验证失败: %w", err)
	}

	// 验证句子记录
	if err := c.validator.ValidateSentenceRecords(data.SentenceRecords); err != nil {
		return fmt.Errorf("句子记录验证失败: %w", err)
	}

	return nil
}

// CheckLegacyRequest 检查传统请求的完整性
func (c *DataIntegrityChecker) CheckLegacyRequest(req *request.DataEpisodeEachReq) error {
	// 验证时间范围
	if err := c.validator.ValidateTimeRange(req.StartTime, req.EndTime); err != nil {
		return fmt.Errorf("时间验证失败: %w", err)
	}

	// 验证学习时长的合理性
	actualDuration := req.EndTime - req.StartTime
	if req.LearnDuration > actualDuration {
		return errors.New("学习时长不能超过实际会话时长")
	}

	// 验证LS次数
	if req.CurrentLsTimes <= 0 {
		return errors.New("当前LS次数必须为正数")
	}

	return nil
}

// ResourceNameResolver 资源名称解析器
type ResourceNameResolver struct {
	videoModel    *model.VideoModel
	resourceModel *model.ResourceModel
}

// NewResourceNameResolver 创建资源名称解析器
func NewResourceNameResolver(videoModel *model.VideoModel, resourceModel *model.ResourceModel) *ResourceNameResolver {
	return &ResourceNameResolver{
		videoModel:    videoModel,
		resourceModel: resourceModel,
	}
}

// GetResourceName 获取资源名称
func (r *ResourceNameResolver) GetResourceName(resourceId string, resourceType int) (string, error) {
	if resourceType == 2 { // LocalResource
		localResource, err := r.videoModel.GetUserLocalResourceById(resourceId)
		if err != nil {
			return "", fmt.Errorf("获取本地资源失败: %w", err)
		}
		if localResource == nil {
			return "", fmt.Errorf("本地资源不存在")
		}
		return localResource.FileName, nil
	} else if resourceType == 1 { // RemoteResource
		resourceRelation, err := r.resourceModel.GetOriginResourceRelation(resourceId)
		if err != nil {
			return "", fmt.Errorf("获取远程资源失败: %w", err)
		}
		if resourceRelation == nil {
			return "", fmt.Errorf("远程资源不存在")
		}
		return resourceRelation.Title, nil
	}
	return "", fmt.Errorf("未知的资源类型: %d", resourceType)
}

// TimeRangeCalculator 时间范围计算器
type TimeRangeCalculator struct{}

// NewTimeRangeCalculator 创建时间范围计算器
func NewTimeRangeCalculator() *TimeRangeCalculator {
	return &TimeRangeCalculator{}
}

// GetTimeRange 根据类型获取时间范围
func (t *TimeRangeCalculator) GetTimeRange(timeType int, startTime, endTime int64) (time.Time, time.Time) {
	switch timeType {
	case 1: // 日
		return t.getDayRange(startTime, endTime)
	case 2: // 周
		return t.getWeekRange(startTime, endTime)
	case 3: // 月
		return t.getMonthRange(startTime, endTime)
	case 4: // 年
		return t.getYearRange(startTime, endTime)
	default:
		return time.Now(), time.Now()
	}
}

func (t *TimeRangeCalculator) getDayRange(startTime, endTime int64) (time.Time, time.Time) {
	var startOfDay, endOfDay time.Time

	if startTime != 0 {
		startOfDay = time.Unix(startTime, 0)
	} else {
		now := time.Now()
		startOfDay = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	}

	if endTime != 0 {
		endOfDay = time.Unix(endTime, 0)
	} else {
		endOfDay = startOfDay.AddDate(0, 0, 1)
	}

	return startOfDay, endOfDay
}

func (t *TimeRangeCalculator) getWeekRange(startTime, endTime int64) (time.Time, time.Time) {
	var startOfWeek, endOfWeek time.Time

	if startTime != 0 {
		startOfWeek = time.Unix(startTime, 0)
	} else {
		now := time.Now()
		startOfWeek = now.AddDate(0, 0, -int(now.Weekday())+1).Truncate(24 * time.Hour)
	}

	if endTime != 0 {
		endOfWeek = time.Unix(endTime, 0)
	} else {
		endOfWeek = startOfWeek.AddDate(0, 0, 7)
	}

	return startOfWeek, endOfWeek
}

func (t *TimeRangeCalculator) getMonthRange(startTime, endTime int64) (time.Time, time.Time) {
	var start, end time.Time

	if startTime != 0 {
		start = time.Unix(startTime, 0)
	} else {
		start = time.Now()
	}

	if endTime != 0 {
		end = time.Unix(endTime, 0)
	} else {
		end = start.AddDate(0, 1, 0)
	}

	return start, end
}

func (t *TimeRangeCalculator) getYearRange(startTime, endTime int64) (time.Time, time.Time) {
	var start, end time.Time

	if startTime != 0 {
		start = time.Unix(startTime, 0)
	} else {
		start = time.Now()
	}

	if endTime != 0 {
		end = time.Unix(endTime, 0)
	} else {
		end = time.Now()
	}

	return start, end
}

// DateFormatter 日期格式化器
type DateFormatter struct{}

// NewDateFormatter 创建日期格式化器
func NewDateFormatter() *DateFormatter {
	return &DateFormatter{}
}

// FormatDateString 格式化日期字符串
func (f *DateFormatter) FormatDateString(t time.Time, isDayData bool) string {
	if isDayData {
		return fmt.Sprintf("%d月%d日 %02d-%02d", t.Month(), t.Day(), t.Hour(), t.Hour()+1)
	}
	return fmt.Sprintf("%d年%d月%d日", t.Year(), t.Month(), t.Day())
}

// FormatDateRange 格式化日期范围
func (f *DateFormatter) FormatDateRange(start, end time.Time) (string, string) {
	startStr := fmt.Sprintf("%d年%d月%d日", start.Year(), start.Month(), start.Day())
	endStr := fmt.Sprintf("%d年%d月%d日", end.Year(), end.Month(), end.Day())
	return startStr, endStr
}

// DataAggregator 数据聚合器
type DataAggregator struct{}

// NewDataAggregator 创建数据聚合器
func NewDataAggregator() *DataAggregator {
	return &DataAggregator{}
}

// CalculateTotalLearnDuration 计算总学习时长
func (a *DataAggregator) CalculateTotalLearnDuration(episodes []*model.DataEpisodeEach) int64 {
	var totalDuration int64
	for _, episode := range episodes {
		totalDuration += episode.LearnDuration
	}
	return totalDuration
}

// AggregateByTimeUnit 按时间单位聚合数据
func (a *DataAggregator) AggregateByTimeUnit(data map[int][]*model.DataEpisodeEach, unitCount int,
	startTime time.Time, timeUnit time.Duration, formatter *DateFormatter, isDayData bool) ([]response.DataEpisodeChartSubResp, int64) {

	results := make([]response.DataEpisodeChartSubResp, unitCount)
	var totalDuration int64

	for i := 0; i < unitCount; i++ {
		episodes := data[i]
		if isDayData {
			episodes = data[i] // 对于小时数据，索引就是小时
		} else {
			episodes = data[i+1] // 对于日/月数据，索引从1开始
		}

		duration := a.CalculateTotalLearnDuration(episodes)
		totalDuration += duration

		var currentTime time.Time
		var startDate, endDate int64

		if isDayData {
			// 小时数据
			currentTime = startTime.Add(time.Duration(i) * timeUnit)
			startDate = currentTime.Unix()
			endDate = currentTime.Add(timeUnit).Unix()
		} else {
			// 日/月数据
			if timeUnit == 24*time.Hour {
				// 日数据
				currentTime = startTime.AddDate(0, 0, i)
				startDate = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location()).Unix()
				endDate = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 23, 59, 59, 999999999, currentTime.Location()).Unix()
			} else {
				// 月数据
				currentTime = time.Date(startTime.Year(), time.Month(i+1), 1, 0, 0, 0, 0, startTime.Location())
				startDate = time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, currentTime.Location()).Unix()
				endDate = time.Date(currentTime.Year(), currentTime.Month()+1, 0, 23, 59, 59, 999999999, currentTime.Location()).Unix()
			}
		}

		results[i] = response.DataEpisodeChartSubResp{
			Duration:        duration,
			StartDate:       startDate,
			EndDate:         endDate,
			StartDateString: formatter.FormatDateString(currentTime, isDayData),
		}

		if !isDayData {
			results[i].EndDateString = formatter.FormatDateString(currentTime, false)
		}
	}

	return results, totalDuration
}

// ErrorHandler 统一错误处理器
type ErrorHandler struct{}

// NewErrorHandler 创建错误处理器
func NewErrorHandler() *ErrorHandler {
	return &ErrorHandler{}
}

// HandleError 处理错误并记录日志
func (h *ErrorHandler) HandleError(err error, context map[string]interface{}, message string) *web.JsonResult {
	logrus.WithFields(logrus.Fields(context)).WithError(err).Error(message)
	return web.JsonInternalError(err)
}

// HandleValidationError 处理验证错误
func (h *ErrorHandler) HandleValidationError(err error, context map[string]interface{}) *web.JsonResult {
	logrus.WithFields(logrus.Fields(context)).WithError(err).Warn("数据验证失败")
	return web.JsonError("数据验证失败: " + err.Error())
}

// HandleNotFoundError 处理资源不存在错误
func (h *ErrorHandler) HandleNotFoundError(resourceType, resourceId string) *web.JsonResult {
	logrus.WithFields(logrus.Fields{
		"resource_type": resourceType,
		"resource_id":   resourceId,
	}).Warn("资源不存在")
	return web.JsonError(fmt.Sprintf("%s不存在", resourceType))
}

// DataCenterService 数据中心服务类（封装通用业务逻辑）
type DataCenterService struct {
	resourceResolver *ResourceNameResolver
	timeCalculator   *TimeRangeCalculator
	dateFormatter    *DateFormatter
	dataAggregator   *DataAggregator
	errorHandler     *ErrorHandler
}

// NewDataCenterService 创建数据中心服务
func NewDataCenterService(videoModel *model.VideoModel, resourceModel *model.ResourceModel) *DataCenterService {
	return &DataCenterService{
		resourceResolver: NewResourceNameResolver(videoModel, resourceModel),
		timeCalculator:   NewTimeRangeCalculator(),
		dateFormatter:    NewDateFormatter(),
		dataAggregator:   NewDataAggregator(),
		errorHandler:     NewErrorHandler(),
	}
}

// GetResourceName 获取资源名称
func (s *DataCenterService) GetResourceName(resourceId string, resourceType int) (string, error) {
	return s.resourceResolver.GetResourceName(resourceId, resourceType)
}

// GetTimeRange 获取时间范围
func (s *DataCenterService) GetTimeRange(timeType int, startTime, endTime int64) (time.Time, time.Time) {
	return s.timeCalculator.GetTimeRange(timeType, startTime, endTime)
}

// FormatDate 格式化日期
func (s *DataCenterService) FormatDate(t time.Time, isDayData bool) string {
	return s.dateFormatter.FormatDateString(t, isDayData)
}

// CalculateDuration 计算时长
func (s *DataCenterService) CalculateDuration(episodes []*model.DataEpisodeEach) int64 {
	return s.dataAggregator.CalculateTotalLearnDuration(episodes)
}

// AggregateData 聚合数据
func (s *DataCenterService) AggregateData(data map[int][]*model.DataEpisodeEach, unitCount int,
	startTime time.Time, timeUnit time.Duration, isDayData bool) ([]response.DataEpisodeChartSubResp, int64) {
	return s.dataAggregator.AggregateByTimeUnit(data, unitCount, startTime, timeUnit, s.dateFormatter, isDayData)
}

// HandleError 处理错误
func (s *DataCenterService) HandleError(err error, context map[string]interface{}, message string) *web.JsonResult {
	return s.errorHandler.HandleError(err, context, message)
}
