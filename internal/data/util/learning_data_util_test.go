package util

import (
	"loop/internal/model"
	"testing"
	"time"
)

func TestLearningDataProcessor_Validate(t *testing.T) {
	processor := NewLearningDataProcessor()

	// 测试有效数据
	validData := &LearningDataUpload{
		SessionData: LearningSessionData{
			ResourceId:           "test_resource_123",
			ResourceType:         1,
			SessionStartTime:     time.Now().Unix() - 3600, // 1小时前
			SessionEndTime:       time.Now().Unix(),
			TotalSessionDuration: 3600000, // 1小时，毫秒
			TotalPlayDuration:    3000000, // 50分钟
			TotalRecordDuration:  600000,  // 10分钟
			LsTimes:              1,
		},
		SentenceRecords: []SentenceLearningRecord{
			{
				SubtitleId:      "subtitle_1",
				TargetPassTimes: 5,
				LearnDuration:   30000, // 30秒
				LearnTimes:      10,
				SubtitleIndex:   0,
			},
		},
	}

	err := processor.Validate(validData)
	if err != nil {
		t.<PERSON>rrorf("Expected valid data to pass validation, got error: %v", err)
	}

	// 测试无效数据 - 播放时长超过总时长
	invalidData := &LearningDataUpload{
		SessionData: LearningSessionData{
			ResourceId:           "test_resource_123",
			ResourceType:         1,
			SessionStartTime:     time.Now().Unix() - 3600,
			SessionEndTime:       time.Now().Unix(),
			TotalSessionDuration: 3600000,
			TotalPlayDuration:    4000000, // 超过总时长
			TotalRecordDuration:  600000,
			LsTimes:              1,
		},
		SentenceRecords: []SentenceLearningRecord{},
	}

	err = processor.Validate(invalidData)
	if err == nil {
		t.Error("Expected invalid data to fail validation")
	}
}

func TestLearningDataProcessor_Process(t *testing.T) {
	processor := NewLearningDataProcessor()

	testData := &LearningDataUpload{
		SessionData: LearningSessionData{
			ResourceId:           "test_resource_123",
			ResourceType:         1,
			SessionStartTime:     time.Now().Unix() - 3600,
			SessionEndTime:       time.Now().Unix(),
			TotalSessionDuration: 3600000, // 1小时，毫秒
			TotalPlayDuration:    3000000,
			TotalRecordDuration:  600000,
			LsTimes:              1,
		},
		SentenceRecords: []SentenceLearningRecord{
			{
				SubtitleId:      "subtitle_1",
				TargetPassTimes: 5,
				LearnDuration:   30000,
				LearnTimes:      10,
				SubtitleIndex:   0,
			},
		},
	}

	result, err := processor.Process("test_user_123", testData)
	if err != nil {
		t.Errorf("Expected processing to succeed, got error: %v", err)
	}

	if result == nil {
		t.Error("Expected result to be non-nil")
		return
	}

	// 验证结果
	if result.DataEpisodeEach.Uid != "test_user_123" {
		t.Errorf("Expected uid to be 'test_user_123', got %s", result.DataEpisodeEach.Uid)
	}

	if result.DataEpisodeEach.ResourceId != "test_resource_123" {
		t.Errorf("Expected resource id to be 'test_resource_123', got %s", result.DataEpisodeEach.ResourceId)
	}

	// 验证时长转换（毫秒转秒）
	expectedDuration := int64(3600) // 3600000毫秒 = 3600秒
	if result.DataEpisodeEach.LearnDuration != expectedDuration {
		t.Errorf("Expected learn duration to be %d, got %d", expectedDuration, result.DataEpisodeEach.LearnDuration)
	}

	// 验证元数据
	if result.Metadata["processor_type"] != "enhanced" {
		t.Error("Expected processor type to be 'enhanced'")
	}

	if result.Metadata["sentence_count"] != 1 {
		t.Errorf("Expected sentence count to be 1, got %v", result.Metadata["sentence_count"])
	}
}

func TestDataIntegrityChecker_CheckLearningDataUpload(t *testing.T) {
	checker := NewDataIntegrityChecker()

	// 测试有效数据
	validData := &LearningDataUpload{
		SessionData: LearningSessionData{
			ResourceId:           "test_resource_123",
			ResourceType:         1,
			SessionStartTime:     time.Now().Unix() - 3600,
			SessionEndTime:       time.Now().Unix(),
			TotalSessionDuration: 3600000,
			TotalPlayDuration:    3000000,
			TotalRecordDuration:  600000,
			LsTimes:              1,
		},
		SentenceRecords: []SentenceLearningRecord{
			{
				SubtitleId:      "subtitle_1",
				TargetPassTimes: 5,
				LearnDuration:   30000,
				LearnTimes:      10,
				SubtitleIndex:   0,
			},
		},
	}

	err := checker.CheckLearningDataUpload(validData)
	if err != nil {
		t.Errorf("Expected valid data to pass integrity check, got error: %v", err)
	}

	// 测试重复的字幕索引
	invalidData := &LearningDataUpload{
		SessionData: validData.SessionData,
		SentenceRecords: []SentenceLearningRecord{
			{
				SubtitleId:      "subtitle_1",
				TargetPassTimes: 5,
				LearnDuration:   30000,
				LearnTimes:      10,
				SubtitleIndex:   0,
			},
			{
				SubtitleId:      "subtitle_2",
				TargetPassTimes: 3,
				LearnDuration:   25000,
				LearnTimes:      8,
				SubtitleIndex:   0, // 重复的索引
			},
		},
	}

	err = checker.CheckLearningDataUpload(invalidData)
	if err == nil {
		t.Error("Expected data with duplicate subtitle indices to fail integrity check")
	}
}

func TestTimeRangeCalculator(t *testing.T) {
	calculator := NewTimeRangeCalculator()

	// 测试日范围计算
	start, end := calculator.GetTimeRange(1, 0, 0) // 使用默认时间
	if start.After(end) {
		t.Error("Start time should be before end time")
	}

	// 验证是同一天
	if start.Day() != end.AddDate(0, 0, -1).Day() {
		t.Error("Day range should span exactly one day")
	}

	// 测试周范围计算
	start, end = calculator.GetTimeRange(2, 0, 0)
	duration := end.Sub(start)
	expectedDuration := 7 * 24 * time.Hour
	if duration != expectedDuration {
		t.Errorf("Week range should be exactly 7 days, got %v", duration)
	}
}

func TestDataAggregator(t *testing.T) {
	aggregator := NewDataAggregator()

	// 创建测试数据
	episodes := []*model.DataEpisodeEach{
		{LearnDuration: 1800}, // 30分钟
		{LearnDuration: 2400}, // 40分钟
		{LearnDuration: 1200}, // 20分钟
	}

	totalDuration := aggregator.CalculateTotalLearnDuration(episodes)
	expectedTotal := int64(5400) // 90分钟
	if totalDuration != expectedTotal {
		t.Errorf("Expected total duration to be %d, got %d", expectedTotal, totalDuration)
	}
}
