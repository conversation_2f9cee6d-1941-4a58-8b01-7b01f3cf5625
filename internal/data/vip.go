package data

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/internal/request"
	"loop/internal/response"
	"loop/pkg/jwtx"
	"loop/pkg/util"
	"loop/pkg/web"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/apple"
	"github.com/jinzhu/copier"
)

func NewVipRepo(
	model *model.VipModel,
	config *config.Config,
) *VipRepo {
	return &VipRepo{
		model:  model,
		config: config,
	}
}

type VipRepo struct {
	model  *model.VipModel
	config *config.Config
}

// app先发起支付  然后等待支付结果
func (s *VipRepo) CreatePayOrder(c *gin.Context, req request.CreatePayOrderReq) *web.JsonResult {
	uid := jwtx.GetUid(c)
	platform := jwtx.GetPlatform(c)
	orderNo, err := s.model.CreatePayOrder(uid, req.ProductId, platform)
	if err != nil {
		return web.JsonInternalError(err)
	}
	createPayOrderResp := response.CreatePayOrderResp{
		UUID: orderNo,
	}
	return web.JsonData(createPayOrderResp)
}

func (s *VipRepo) GetOrderList(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)
	var tradeOrderList []*model.UserPurchaseOrder
	err := s.model.GetList(&tradeOrderList, model.UserPurchaseOrder{Uid: uid})
	if err != nil {
		return web.JsonInternalError(err)
	}
	var tradeOrderResps []response.TradeOrderResp
	copier.Copy(&tradeOrderResps, tradeOrderList)
	return web.JsonData(tradeOrderResps)
}

func (s *VipRepo) GetProductList(c *gin.Context) *web.JsonResult {
	var tradeProductList []*model.TradeProduct
	platform := jwtx.GetPlatform(c)
	switch platform {
	case util.PlatformIOS:
		s.model.GetList(&tradeProductList, model.TradeProduct{Terminal: 2})
	case util.PlatformAndroid:
		s.model.GetList(&tradeProductList, model.TradeProduct{Terminal: 1})
	}
	var productListItemResp []response.ProductListItemResp
	copier.Copy(&productListItemResp, tradeProductList)
	return web.JsonData(productListItemResp)
}

func (s *VipRepo) ExchangeCode(c *gin.Context, req request.ExchangeCodeReq) *web.JsonResult {
	uid := ""
	if req.Uid == "" {
		uid = jwtx.GetUid(c)
	} else {
		uid = req.Uid
	}
	err := s.model.ExchangeCode(c, uid, req.Code)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}
func (s *VipRepo) AddPromotionCode(c *gin.Context, req request.AddPromotionCodeReq) *web.JsonResult {
	err := s.model.AddPromotionCode(req.Days)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}
func (s *VipRepo) DeletePromotionCode(c *gin.Context, req request.IdReq) *web.JsonResult {
	return DeleteById[model.PromotionCode](s.model.DbModel, req.Id)
}
func (s *VipRepo) DeleteMultiPromotionCode(c *gin.Context, req request.IdsReq) *web.JsonResult {
	return DeleteMultiId[model.PromotionCode](s.model.DbModel, req.Id)
}
func (s *VipRepo) GetPromotionCodeList(c *gin.Context, req request.ListReq) *web.JsonResult {
	var promotionCodeList []*model.PromotionCode
	count, err := s.model.GetListPage(&promotionCodeList, req.CurrentPage, req.PageSize, "")
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonData(web.PageJsonResult{
		Data:  promotionCodeList,
		Total: count,
	})
}

func (s *VipRepo) GetUserVipInfo(c *gin.Context) *web.JsonResult {
	uid := jwtx.GetUid(c)
	var userVipResp response.UserVipResp
	userVIPRelations := model.UserVIPRelations{}
	found, err := s.model.GetOne(&userVIPRelations, model.UserVIPRelations{Uid: uid})
	if err != nil {
		return web.JsonInternalError(err)
	}
	if !found {
		return web.JsonEmptyData()
	}
	if found {
		copier.Copy(&userVipResp, userVIPRelations)
	}
	userSubscription := model.UserSubscription{}
	found, err = s.model.GetOne(&userSubscription, model.UserSubscription{Uid: uid})
	if err != nil {
		return web.JsonInternalError(err)
	}
	if found {
		userVipResp.NextCycleAmount = userSubscription.NextCycleAmount
		userVipResp.NextPaidDate = userSubscription.NextPaidDate
		userVipResp.NextPaidTimestamp = userSubscription.NextPaidTimestamp
	}

	return web.JsonData(userVipResp)
}

func (s *VipRepo) ReceiveApplePayResult(c *gin.Context, notificationV2Payload *apple.NotificationV2Payload, renewalInfo *apple.RenewalInfo, transactionInfo *apple.TransactionInfo) *web.JsonResult {
	err := s.model.ReceiveApplePayResult(notificationV2Payload, renewalInfo, transactionInfo)
	if err != nil {
		return web.JsonInternalError(err)
	}
	return web.JsonOK()
}

// func (s *VipRepo) GetPayStatus(c *gin.Context, productId string) *web.JsonResult {

// }
