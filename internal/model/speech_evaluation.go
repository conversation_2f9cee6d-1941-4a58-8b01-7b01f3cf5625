package model

import (
	"loop/internal/config"

	"go4.org/syncutil/singleflight"
	"gorm.io/gorm"
)

type SpeechEvaluation struct {
	ModelAutoId
	Uid          string `gorm:"not null;index:idx_user_speech_eval,priority:1" json:"uid"`
	ResourceId   string `gorm:"not null" json:"resourceId"`                 // 资源ID
	ResourceType int    `gorm:"type:tinyint;default:0" json:"resourceType"` // 资源类型
	Content      string `gorm:"type:LONGTEXT" json:"content"`               // 评测内容（json字符串）
	AudioUrl     string `gorm:"type:varchar(512)" json:"audioUrl"`          // 音频地址
	StartTime    int64  `gorm:"type:bigint;default:0" json:"startTime"`     // 开始时间
	EndTime      int64  `gorm:"type:bigint;default:0" json:"endTime"`       // 结束时间
}

func (SpeechEvaluation) TableName() string {
	return "speech_evaluations"
}

type SpeechEvaluationModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func NewSpeechEvaluationModel(dbModel *DbModel, config *config.Config) *SpeechEvaluationModel {
	return &SpeechEvaluationModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{},
	}
}

func (m *SpeechEvaluationModel) GetEvaluationsByResource(resourceId string, resourceType int) ([]*SpeechEvaluation, error) {
	var list []*SpeechEvaluation
	err := m.GetList(&list, &SpeechEvaluation{ResourceId: resourceId, ResourceType: resourceType})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (m *SpeechEvaluationModel) Create(evaluation *SpeechEvaluation) error {
	return m.SaveOne(evaluation)
}

func (m *SpeechEvaluationModel) Update(evaluation *SpeechEvaluation) error {
	return m.DbModel.Update(evaluation, "id = ?", evaluation.Id)
}

// CreateBatch 批量创建语音评测记录
func (m *SpeechEvaluationModel) CreateBatch(evaluations []*SpeechEvaluation) error {
	if len(evaluations) == 0 {
		return nil
	}
	return m.DB.CreateInBatches(evaluations, len(evaluations)).Error
}

// UpdateBatch 批量更新语音评测记录
func (m *SpeechEvaluationModel) UpdateBatch(evaluations []*SpeechEvaluation) error {
	if len(evaluations) == 0 {
		return nil
	}

	return m.DB.Transaction(func(tx *gorm.DB) error {
		for _, evaluation := range evaluations {
			if err := tx.Model(&SpeechEvaluation{}).Where("id = ?", evaluation.Id).Updates(evaluation).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// UpdateBatchAudioUrl 批量更新AudioUrl
func (m *SpeechEvaluationModel) UpdateBatchAudioUrl(uid string, evaluations []*SpeechEvaluation) error {
	if len(evaluations) == 0 {
		return nil
	}

	return m.DB.Transaction(func(tx *gorm.DB) error {
		for _, evaluation := range evaluations {
			if evaluation.Id == 0 {
				continue
			}
			// 直接通过id查找并更新audioUrl
			if err := tx.Model(&SpeechEvaluation{}).Where("id = ? AND uid = ?", evaluation.Id, uid).Update("audio_url", evaluation.AudioUrl).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
