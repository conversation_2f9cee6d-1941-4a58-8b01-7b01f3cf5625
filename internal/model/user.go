package model

import (
	"context"
	"fmt"
	"loop/internal/config"
	"loop/internal/request"
	"loop/pkg/dbx"
	"loop/pkg/types"
	"time"

	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
	"go4.org/syncutil/singleflight"
)

type User struct {
	Model
	Username            string `gorm:"size:64;not null;column:username;comment:用户名"`
	PasswordDigest      string `gorm:"size:128;comment:密码"`
	Nickname            string `gorm:"size:128;default:null;comment:昵称"`
	Status              int    `gorm:"size:20;default:0;comment:状态 0正常 1禁用 2已注销"`
	AppleUserIdentifier string `gorm:"size:128;comment:苹果登录的唯一码"`
	Avatar              string `gorm:"size:1000;comment:用户头像"`
	NativeLangCode      string `gorm:"size:128;comment:用户母语"`
	TargetLangCode      string `gorm:"size:128;comment:用户想要学习的语言"`
	VipLevelId          string `gorm:"size:20;comment:VIP等级ID"`
}

func (User) TableName() string {
	return "users"
}

// 用户的播放器配置
type UserPlayerConfig struct {
	Model
	Uid                           string         `gorm:"not null;index:idx_user_watch,priority:1"`
	SubtitleTextBottomHeightRatio float32        `gorm:"size:20;not null;comment:字幕文件底部距离与整个播放器的比例"`
	ShowSubtitleWhenRecordEnd     bool           `gorm:"size:20;not null;comment:录制结束后显示字幕"`
	AutoPlayRecordWhenRecordEnd   bool           `gorm:"size:20;not null;comment:录制结束后自动播放录制声音"`
	AutoRecord                    bool           `gorm:"size:20;not null;comment:自动开始录制"`
	AutoStopRecord                bool           `gorm:"size:20;not null;comment:自动停止录制"`
	OpenSingleRepeat              bool           `gorm:"size:20;not null;comment:单句循环播放"`
	SubtitleFontSize              int            `gorm:"size:20;not null;comment:字幕文字大小"`
	SingleRepeatCount             int            `gorm:"size:20;not null;comment:单句重复次数,0 代表 无限次数"`
	ShowSubtitleNum               int            `gorm:"size:20;not null;comment:是否显示字幕序号0不显示1显示"`
	CoverSubtitle                 bool           `gorm:"size:20;not null;comment:是否打开字幕遮挡"`
	MenuSort                      types.IntArray `gorm:"type:json;not null;comment:播放器里的菜单顺序,为空就是默认"`
}

func (UserPlayerConfig) TableName() string {
	return "user_player_configs"
}

// WatchHistory 定义了用户观看历史的结构体
type WatchHistory struct {
	Model
	Uid          string `gorm:"not null;index:idx_user_watch,priority:1"`
	ResourceId   string `gorm:"not null"`               // 视频资源ID
	ResourceType int    `gorm:"type:tinyint;default:0"` //视频类型 1代表远程的资源 2代表本地资源
	Position     int64  `gorm:"type:bigint;default:0"`  // 用户停止观看的位置
}

func (WatchHistory) TableName() string {
	return "watch_historys"
}

// 已删除用户记录表
type DeletedUser struct {
	ModelAutoId
	Uid                  string `gorm:"not null;index:idx_user_watch,priority:1"`
	Username             string `gorm:"size:64;not null;comment:用户名"`
	RegisterAt           int64  `gorm:"not null;comment:注册时间(时间戳)"`
	DeletedAt            int64  `gorm:"not null;comment:注销时间(时间戳)"`
	RegisterToDeleteDays int    `gorm:"not null;comment:注册到注销的天数"`
	DeviceInfo           string `gorm:"size:255;comment:用户设备信息"`
	Reason               string `gorm:"size:255;comment:注销原因"`
	VipLevelId           string `gorm:"size:20;comment:注销时会员等级"`
	VipDaysLeft          int    `gorm:"comment:注销时会员剩余天数"`
	VipHistory           string `gorm:"type:text;comment:历史开通过的会员记录"`
}

func (DeletedUser) TableName() string {
	return "deleted_users"
}

func NewUserModel(dbModel *DbModel, config *config.Config) *UserModel {
	return &UserModel{
		DbModel: dbModel,
		config:  config,
		sg:      &singleflight.Group{}}
}

type UserModel struct {
	*DbModel
	config *config.Config
	sg     *singleflight.Group
}

func (r *UserModel) GetUserByUId(ctx context.Context, uid string) (*User, error) {
	res, err := r.sg.Do(fmt.Sprintf("get_user_by_uid_%s", uid), func() (interface{}, error) {
		user := User{}
		if found, err := r.GetOne(&user, "id = ?", uid); !found {
			return nil, err
		}
		return &user, nil
	})
	if err != nil {
		return nil, err
	}
	data, ok := res.(*User)
	if !ok {
		return nil, fmt.Errorf("type assertion to *DATA failed")
	}
	return data, nil
}

func (r *UserModel) FindByUsername(username string) (*User, error) {
	query := User{Username: username}
	user := User{}
	if found, err := r.GetOne(&user, query); !found {
		return nil, err
	}
	return &user, nil
}

func (r *UserModel) FindByAppleSign(appleUserIdentifier string) (*User, error) {
	query := User{AppleUserIdentifier: appleUserIdentifier}
	user := User{}
	found, err := r.GetOne(&user, query)
	if err != nil {
		return nil, err
	}
	if !found {
		return nil, nil
	}
	return &user, nil
}
func (r *UserModel) GetPlayerConfig(uid string) (*UserPlayerConfig, error) {
	query := UserPlayerConfig{Uid: uid}
	result := UserPlayerConfig{}
	if found, err := r.GetOne(&result, query); !found {
		if err != nil {
			return nil, err
		}
		return nil, nil
	}
	return &result, nil
}
func (r *UserModel) AddOrUpdateWatchHistory(uid string, req request.WatchHistoryAddReq) error {
	query := WatchHistory{Uid: uid, ResourceId: req.ResourceId, ResourceType: req.ResourceType}
	saver := WatchHistory{}
	found, err := r.GetOne(&saver, query)
	if err != nil {
		return err
	}
	saver.Position = req.Position
	if found {
		logrus.Info("User ", uid, " update WatchHistory id=", saver.Id)
		if err := r.Update(&saver, "id = ?", saver.Id); err != nil {
			return err
		}
	} else {
		logrus.Info("User ", uid, " save WatchHistory")
		copier.Copy(&saver, &query)
		err = r.SaveOne(&saver)
		if err != nil {
			return err
		}
	}

	return nil
}
func (r *UserModel) DeleteWatchHistory(uid string, resourceId string, resourceType int) error {
	err := r.Tx(
		func(txDb *dbx.DBExtension) error {
			if err := txDb.Delete(&WatchHistory{}, WatchHistory{Uid: uid, ResourceId: resourceId, ResourceType: resourceType}).Error; err != nil {
				return err
			}
			return nil
		},
	)
	return err

}
func (r *UserModel) RegisterUser(user User, langCode string) (*User, error) {
	var newUser User
	copier.Copy(&newUser, &user)
	newUser.NativeLangCode = langCode
	if err := r.SaveOne(&newUser); err != nil {
		return nil, err
	}
	return &newUser, nil
}

func (r *UserModel) DeleteAccount(uid string, reason string, deviceInfo string) error {
	return r.Tx(
		func(txDb *dbx.DBExtension) error {
			// 1. 查出用户信息
			var user User
			var username string
			var registerAt int64
			if found, _ := txDb.GetOne(&user, "id = ?", uid); found {
				username = user.Username
				registerAt = user.CreatedAt.Unix()
			}

			// 2. 查会员信息
			var userVIPRelations UserVIPRelations
			var vipLevelId string
			var vipDaysLeft int
			if found, _ := txDb.GetOne(&userVIPRelations, UserVIPRelations{Uid: uid}); found {
				vipLevelId = fmt.Sprintf("%d", userVIPRelations.VipID)
				// 剩余天数 = (到期时间戳-当前时间戳)/86400_000
				nowMs := time.Now().UnixMilli()
				if userVIPRelations.ExpireTimestamp > nowMs {
					vipDaysLeft = int((userVIPRelations.ExpireTimestamp - nowMs) / (1000 * 60 * 60 * 24))
				} else {
					vipDaysLeft = 0
				}
			}

			// 3. 查会员历史流水
			var vipFlows []UserVIPFlow
			var vipHistory string
			if err := txDb.Where(&UserVIPFlow{Uid: uid}).Order("operation_timestamp asc").Find(&vipFlows).Error; err == nil {
				for _, flow := range vipFlows {
					vipHistory += fmt.Sprintf("%s | %d | %d\n", flow.Title, flow.Operation, flow.OperationTimestamp)
				}
			}

			// 4. 注销时间、注册到注销天数
			deletedAt := time.Now().Unix()
			registerToDeleteDays := 0
			if registerAt > 0 {
				registerToDeleteDays = int((deletedAt - registerAt) / (60 * 60 * 24))
			}

			// 5. 物理删除用户
			if err := txDb.Where(&User{Model: Model{Id: uid}}).Delete(&User{}).Error; err != nil {
				return err
			}

			// 6. 记录已删除用户
			deletedUser := DeletedUser{
				Uid:                  uid,
				Username:             username,
				RegisterAt:           registerAt,
				DeletedAt:            deletedAt,
				RegisterToDeleteDays: registerToDeleteDays,
				DeviceInfo:           deviceInfo,
				Reason:               reason,
				VipLevelId:           vipLevelId,
				VipDaysLeft:          vipDaysLeft,
				VipHistory:           vipHistory,
			}
			if err := txDb.Create(&deletedUser).Error; err != nil {
				logrus.Errorf("记录已删除用户异常: uid=%s, err=%v", uid, err)
				return err
			}

			// 2. 删除用户订阅信息
			if err := txDb.Model(&UserSubscription{}).Where(&UserSubscription{Uid: uid}).Updates(&UserSubscription{
				Status: UserSubscriptionStatusTermination,
				Desc:   "用户主动删除账号",
			}).Error; err != nil {
				logrus.Errorf("删除用户订阅信息异常: uid=%s, err=%v", uid, err)
				return err
			}

			// 3. 删除用户会员关系
			if err := txDb.Where(&UserVIPRelations{Uid: uid}).Delete(&UserVIPRelations{}).Error; err != nil {
				logrus.Errorf("删除用户会员关系: uid=%s, err=%v", uid, err)
				return err
			}

			// 4. 删除用户配置信息
			if err := txDb.Where(&UserPlayerConfig{Uid: uid}).Delete(&UserPlayerConfig{}).Error; err != nil {
				return err
			}

			// 5. 删除观看历史
			if err := txDb.Where(&WatchHistory{Uid: uid}).Delete(&WatchHistory{}).Error; err != nil {
				return err
			}

			// 6. 删除字幕关系
			if err := txDb.Where(&UserSubtitleRelations{Uid: uid}).Delete(&UserSubtitleRelations{}).Error; err != nil {
				return err
			}

			// 7. 删除本地资源关系
			if err := txDb.Where(&UserLocalResource{Uid: uid}).Delete(&UserLocalResource{}).Error; err != nil {
				return err
			}

			// 8. 删除笔记收藏关系
			if err := txDb.Where(&NoteCollectRelations{Uid: uid}).Delete(&NoteCollectRelations{}).Error; err != nil {
				return err
			}

			// 9. 删除远程资源关系
			if err := txDb.Where(&UserRemoteResourceRelations{Uid: uid}).Delete(&UserRemoteResourceRelations{}).Error; err != nil {
				return err
			}

			// 10. 删除学习数据
			if err := txDb.Where(&DataEpisode{Uid: uid}).Delete(&DataEpisode{}).Error; err != nil {
				return err
			}
			if err := txDb.Where(&DataEpisodeEach{Uid: uid}).Delete(&DataEpisodeEach{}).Error; err != nil {
				return err
			}

			return nil
		},
	)
}
